<?php

function migrate_fix_license_files_fk_reference(PDO $pdo): bool
{
    $migration_name = 'fix_license_files_fk_reference';

    try {
        
        $check_sql = "SELECT 1 FROM migrations WHERE name = :name";
        $stmt_check = $pdo->prepare($check_sql);
        $stmt_check->execute([':name' => $migration_name]);
        if ($stmt_check->fetch()) {
            return true; 
        }

        $pdo->exec('PRAGMA foreign_keys=OFF;');
        $pdo->beginTransaction();

        
        $pdo->exec("
            CREATE TABLE license_files_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                digital_product_id INTEGER NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
                FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE
            );
        ");

        
        
        $pdo->exec("INSERT INTO license_files_new (id, license_id, digital_product_id, created_at)
                      SELECT id, license_id, digital_product_id, created_at FROM license_files;");

        
        $pdo->exec("DROP TABLE license_files;");

        
        $pdo->exec("ALTER TABLE license_files_new RENAME TO license_files;");

        $pdo->commit();
        $pdo->exec('PRAGMA foreign_keys=ON;');

        
        $stmt_insert_migration = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now'))");
        $stmt_insert_migration->execute([':name' => $migration_name]);
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $pdo->exec('PRAGMA foreign_keys=ON;'); 
        return false;
    }
}

if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    require_once __DIR__ . '/../db.php'; 
    $pdo = get_db_connection();
    if ($pdo) {
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            executed_at TEXT NOT NULL
        )");
        
        if (migrate_fix_license_files_fk_reference($pdo)) {
            echo "Migration fix_license_files_fk_reference executed successfully.\n";
        } else {
            echo "Migration fix_license_files_fk_reference failed.\n";
        }
    } else {
        echo "Failed to connect to the database.\n";
    }
}
?>