<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}

$is_editing = isset($sitemap) && is_array($sitemap);
$form_title = $is_editing ? 'Editar Configuração de Sitemap/XML' : 'Nova Configuração de Sitemap/XML';
$form_action = $is_editing ? 'update' : 'create';
$sitemap_id = $is_editing ? $sitemap['id'] : '';

if (!$is_editing) {
    $sitemap = [
        'name' => '',
        'type' => 'sitemap',
        'output_path' => '',
        'include_products' => 1,
        'include_blog' => 1,
        'include_pages' => 1,
        'custom_config_json' => '',
        'is_active' => 1
    ];
}

$default_directory = get_setting('sitemap_default_directory', '');

display_flash_messages();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><?= $form_title ?></h1>
    <a href="admin.php?section=sitemaps&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Voltar à Lista
    </a>
</div>

<div class="card">
    <div class="card-header">
        <i class="bi bi-diagram-3"></i> Detalhes da Configuração
    </div>
    <div class="card-body">
        <form method="POST" action="admin.php?section=sitemaps&action=<?= $form_action ?>&<?= get_session_id_param() ?>" class="needs-validation" novalidate data-no-ajax="true">
            <?= csrf_input_field() ?>
            <!-- Debug info for troubleshooting -->
            <input type="hidden" name="debug_session_id" value="<?= session_id() ?>">

            <?php if ($is_editing): ?>
                <input type="hidden" name="id" value="<?= $sitemap_id ?>">
            <?php endif; ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="name" class="form-label">Nome *</label>
                    <input type="text" class="form-control" id="name" name="name" value="<?= sanitize_input($sitemap['name']) ?>" required>
                    <div class="invalid-feedback">Por favor, insira um nome.</div>
                </div>

                <div class="col-md-6">
                    <label for="type" class="form-label">Tipo *</label>
                    <select class="form-select" id="type" name="type" required>
                        <option value="sitemap" <?= $sitemap['type'] === 'sitemap' ? 'selected' : '' ?>>Sitemap</option>
                        <option value="google_merchant" <?= $sitemap['type'] === 'google_merchant' ? 'selected' : '' ?>>Google Merchant</option>
                        <option value="atom" <?= $sitemap['type'] === 'atom' ? 'selected' : '' ?>>Atom Feed</option>
                        <option value="custom" <?= $sitemap['type'] === 'custom' ? 'selected' : '' ?>>XML Personalizado</option>
                    </select>
                    <div class="invalid-feedback">Por favor, selecione um tipo.</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="output_path" class="form-label">Caminho de Saída *</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="output_path" name="output_path" value="<?= sanitize_input($sitemap['output_path']) ?>" required>
                        <?php if (!empty($default_directory)): ?>
                            <button class="btn btn-outline-secondary" type="button" id="use_default_dir">Usar Diretório Padrão</button>
                        <?php endif; ?>
                    </div>
                    <div class="form-text">
                        Caminho absoluto ou relativo ao diretório raiz do projeto.
                        <?php if (!empty($default_directory)): ?>
                            <br>Diretório padrão: <code><?= sanitize_input($default_directory) ?></code>
                        <?php endif; ?>
                    </div>
                    <div class="invalid-feedback">Por favor, insira um caminho de saída.</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label class="form-label">Conteúdo a Incluir</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_products" name="include_products" value="1" <?= $sitemap['include_products'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="include_products">Incluir Produtos</label>
                    </div>

                    <!-- Product type options (visible only for Google Merchant) -->
                    <div class="product-types-container ml-4" style="margin-left: 2rem; display: <?= $sitemap['type'] === 'google_merchant' ? 'block' : 'none' ?>;">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_regular_products" name="include_regular_products" value="1" <?= isset($sitemap['include_regular_products']) ? ($sitemap['include_regular_products'] ? 'checked' : '') : 'checked' ?>>
                            <label class="form-check-label" for="include_regular_products">Produtos Simples</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_variation_products" name="include_variation_products" value="1" <?= isset($sitemap['include_variation_products']) ? ($sitemap['include_variation_products'] ? 'checked' : '') : 'checked' ?>>
                            <label class="form-check-label" for="include_variation_products">Produtos com Variações</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_digital_products" name="include_digital_products" value="1" <?= isset($sitemap['include_digital_products']) ? ($sitemap['include_digital_products'] ? 'checked' : '') : 'checked' ?>>
                            <label class="form-check-label" for="include_digital_products">Produtos Digitais</label>
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_blog" name="include_blog" value="1" <?= $sitemap['include_blog'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="include_blog">Incluir Blog</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_pages" name="include_pages" value="1" <?= $sitemap['include_pages'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="include_pages">Incluir Páginas</label>
                    </div>
                </div>
            </div>

            <div class="row mb-3 custom-config-container" style="display: <?= $sitemap['type'] === 'custom' ? 'block' : 'none' ?>;">
                <div class="col-md-12">
                    <label for="custom_config_json" class="form-label">Configuração Personalizada (JSON)</label>
                    <textarea class="form-control" id="custom_config_json" name="custom_config_json" rows="10"><?= sanitize_input($sitemap['custom_config_json']) ?></textarea>
                    <div class="form-text">
                        <p>Configuração em formato JSON para XMLs personalizados. Utilize o formato abaixo:</p>
                        <div class="card mt-2 mb-2">
                            <div class="card-header">
                                <strong>Estrutura do JSON</strong>
                            </div>
                            <div class="card-body">
                                <pre class="mb-0" style="font-size: 0.85rem;">{
  "root_element": "nome_do_elemento_raiz",
  "root_attributes": {
    "xmlns": "http://exemplo.com/namespace",
    "outro_atributo": "valor"
  },
  "items": [
    {
      "element": "item",
      "attributes": {
        "id": "1"
      },
      "content": "Conteúdo do item",
      "children": [
        {
          "element": "titulo",
          "content": "Título do item"
        }
      ]
    }
  ],
  "custom_queries": [
    {
      "sql": "SELECT * FROM products WHERE is_active = 1",
      "item_element": "produto",
      "fields": [
        {
          "name": "id",
          "column": "id"
        },
        {
          "name": "nome",
          "column": "name_pt"
        },
        {
          "name": "url",
          "column": "slug",
          "transform": "url"
        }
      ]
    }
  ]
}</pre>
                            </div>
                        </div>
                        <p><strong>Placeholders disponíveis:</strong></p>
                        <ul>
                            <li><code>{site_url}</code> - URL base do site</li>
                            <li><code>{date}</code> - Data atual (formato YYYY-MM-DD)</li>
                        </ul>
                        <p><strong>Transformações disponíveis:</strong></p>
                        <ul>
                            <li><code>url</code> - Adiciona o URL base do site ao valor</li>
                            <li><code>date</code> - Formata o valor como data (YYYY-MM-DD)</li>
                            <li><code>html_escape</code> - Escapa caracteres HTML especiais</li>
                        </ul>
                        <p><strong>Exemplo para produtos:</strong></p>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="example-products-btn">Inserir exemplo de produtos</button>
                        <p class="mt-2"><strong>Exemplo para blog:</strong></p>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="example-blog-btn">Inserir exemplo de blog</button>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?= $sitemap['is_active'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_active">Ativo</label>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <a href="admin.php?section=sitemaps&<?= get_session_id_param() ?>" class="btn btn-outline-secondary me-2">Cancelar</a>
                <button type="submit" class="btn btn-primary" id="save-sitemap-btn">Guardar</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    const saveBtn = document.getElementById('save-sitemap-btn');

    // Add click event to the save button
    saveBtn.addEventListener('click', function(event) {
        event.preventDefault();

        // Validate form
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // Ensure the session ID is in the form action URL
        const formAction = form.getAttribute('action');
        if (formAction && !formAction.includes('sid=')) {
            const sessionId = getSessionId();
            if (sessionId) {
                const newAction = formAction + (formAction.includes('?') ? '&' : '?') + 'sid=' + sessionId;
                form.setAttribute('action', newAction);
            }
        }

        // Submit the form
        form.submit();
    });

    // Regular form submit handler
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            form.classList.add('was-validated');
        } else {
            // Log form submission for debugging

            // Ensure the session ID is in the form action URL
            const formAction = form.getAttribute('action');
            if (formAction && !formAction.includes('sid=')) {
                const sessionId = getSessionId();
                if (sessionId) {
                    const newAction = formAction + (formAction.includes('?') ? '&' : '?') + 'sid=' + sessionId;
                    form.setAttribute('action', newAction);
                }
            }
        }
    });

    // Show/hide custom config and product types based on type
    const typeSelect = document.getElementById('type');
    const customConfigContainer = document.querySelector('.custom-config-container');
    const productTypesContainer = document.querySelector('.product-types-container');

    typeSelect.addEventListener('change', function() {
        customConfigContainer.style.display = this.value === 'custom' ? 'block' : 'none';
        productTypesContainer.style.display = this.value === 'google_merchant' ? 'block' : 'none';
    });

    // Example buttons for custom XML
    const exampleProductsBtn = document.getElementById('example-products-btn');
    const exampleBlogBtn = document.getElementById('example-blog-btn');
    const customConfigTextarea = document.getElementById('custom_config_json');

    if (exampleProductsBtn) {
        exampleProductsBtn.addEventListener('click', function() {
            const productsExample = {
                "root_element": "produtos",
                "root_attributes": {
                    "xmlns": "http://www.sitemaps.org/schemas/sitemap/0.9",
                    "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
                    "xsi:schemaLocation": "http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"
                },
                "custom_queries": [
                    {
                        "sql": "SELECT id, name_pt, slug, description_pt, base_price, updated_at FROM products WHERE is_active = 1",
                        "item_element": "produto",
                        "fields": [
                            {
                                "name": "id",
                                "column": "id"
                            },
                            {
                                "name": "nome",
                                "column": "name_pt",
                                "transform": "html_escape"
                            },
                            {
                                "name": "url",
                                "column": "slug",
                                "transform": "url"
                            },
                            {
                                "name": "descricao",
                                "column": "description_pt",
                                "transform": "html_escape"
                            },
                            {
                                "name": "preco",
                                "column": "base_price"
                            },
                            {
                                "name": "ultima_atualizacao",
                                "column": "updated_at",
                                "transform": "date"
                            }
                        ]
                    }
                ]
            };

            if (confirm('Isto irá substituir qualquer configuração existente. Continuar?')) {
                customConfigTextarea.value = JSON.stringify(productsExample, null, 2);
            }
        });
    }

    if (exampleBlogBtn) {
        exampleBlogBtn.addEventListener('click', function() {
            const blogExample = {
                "root_element": "blog",
                "root_attributes": {
                    "xmlns": "http://www.sitemaps.org/schemas/sitemap/0.9",
                    "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
                    "xsi:schemaLocation": "http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"
                },
                "items": [
                    {
                        "element": "info",
                        "children": [
                            {
                                "element": "website",
                                "content": "{site_url}"
                            },
                            {
                                "element": "data_geracao",
                                "content": "{date}"
                            }
                        ]
                    }
                ],
                "custom_queries": [
                    {
                        "sql": "SELECT id, title, slug, excerpt, published_at, updated_at FROM blog_posts WHERE is_published = 1",
                        "item_element": "post",
                        "fields": [
                            {
                                "name": "id",
                                "column": "id"
                            },
                            {
                                "name": "titulo",
                                "column": "title",
                                "transform": "html_escape"
                            },
                            {
                                "name": "url",
                                "column": "slug",
                                "transform": "url"
                            },
                            {
                                "name": "resumo",
                                "column": "excerpt",
                                "transform": "html_escape"
                            },
                            {
                                "name": "data_publicacao",
                                "column": "published_at",
                                "transform": "date"
                            },
                            {
                                "name": "ultima_atualizacao",
                                "column": "updated_at",
                                "transform": "date"
                            }
                        ]
                    }
                ]
            };

            if (confirm('Isto irá substituir qualquer configuração existente. Continuar?')) {
                customConfigTextarea.value = JSON.stringify(blogExample, null, 2);
            }
        });
    }

    // Use default directory button
    const useDefaultDirBtn = document.getElementById('use_default_dir');
    if (useDefaultDirBtn) {
        useDefaultDirBtn.addEventListener('click', function() {
            const outputPathInput = document.getElementById('output_path');
            const defaultDir = '<?= addslashes($default_directory) ?>';
            const fileName = getDefaultFileName();

            outputPathInput.value = defaultDir + (defaultDir.endsWith('/') || defaultDir.endsWith('\\') ? '' : '/') + fileName;
        });
    }

    // Get default filename based on type
    function getDefaultFileName() {
        const type = document.getElementById('type').value;
        switch (type) {
            case 'sitemap':
                return 'sitemap.xml';
            case 'google_merchant':
                return 'google_merchant_feed.xml';
            case 'custom':
                return 'custom.xml';
            default:
                return 'sitemap.xml';
        }
    }

    // Helper function to get session ID from URL or window variable
    function getSessionId() {
        // Try to get from window variable first
        if (window.eshopSessionId) {
            return window.eshopSessionId;
        }

        // Try to extract from URL
        const urlParams = new URLSearchParams(window.location.search);
        const sidParam = urlParams.get('sid');
        if (sidParam) {
            return sidParam;
        }

        // Try to get from debug field
        const debugSessionId = document.querySelector('input[name="debug_session_id"]')?.value;
        if (debugSessionId) {
            return debugSessionId;
        }

        return null;
    }
});
</script>
