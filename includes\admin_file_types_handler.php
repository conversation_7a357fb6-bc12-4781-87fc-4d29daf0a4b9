<?php

if (!defined('ADMIN_AJAX_HANDLER')) {
    die('Direct access not allowed');
}

if ($action === 'get_file_types' && isset($_GET['file_id'])) {
    require_once __DIR__ . '/digital_files_functions.php';
    
    $file_id = (int)$_GET['file_id'];
    if ($file_id <= 0) {
        echo '<span class="text-muted">ID de arquivo inválido</span>';
        exit;
    }
    
    try {
        
        $file_types = get_digital_file_file_types($file_id);
        
        if (!empty($file_types)) {
            foreach ($file_types as $type) {
                echo '<span class="badge bg-info text-dark">' . sanitize_input($type['extension']) . '</span> ';
            }
        } else {
            echo '<span class="text-muted">Nenhum tipo definido</span>';
        }
    } catch (Exception $e) {
        echo '<span class="text-danger">Erro ao obter tipos de arquivo</span>';
    }
    exit;
}
