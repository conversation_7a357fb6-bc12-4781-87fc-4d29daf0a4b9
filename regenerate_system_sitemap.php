<?php
require_once 'includes/functions.php';
require_once 'includes/sitemap_functions.php';

if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}
if (!isset($_SERVER['SERVER_NAME'])) {
    $_SERVER['SERVER_NAME'] = 'localhost';
}
if (!isset($_SERVER['SERVER_PORT'])) {
    $_SERVER['SERVER_PORT'] = '80';
}
if (!isset($_SERVER['HTTPS'])) {
    $_SERVER['HTTPS'] = '';
}
if (!isset($_SERVER['REQUEST_URI'])) {
    $_SERVER['REQUEST_URI'] = '/';
}

echo "Regenerating sitemap using system functions...\n";

$configs = get_sitemap_configs(true);

if (empty($configs)) {
    echo "No active sitemap configurations found.\n";
    exit(1);
}

foreach ($configs as $config) {
    echo "Generating sitemap: {$config['name']} ({$config['type']})\n";
    
    $result = null;
    
    switch ($config['type']) {
        case 'sitemap':
            $result = generate_standard_sitemap($config);
            break;
        case 'atom':
            $result = generate_atom_feed($config);
            break;
        case 'google_merchant':
            
            echo "Skipping Google Merchant sitemap\n";
            continue 2;
        default:
            echo "Unknown sitemap type: {$config['type']}\n";
            continue 2;
    }
    
    if ($result && $result['success']) {
        echo "Successfully generated: {$config['output_path']}\n";
    } else {
        echo "Failed to generate: {$config['output_path']}\n";
        if ($result && isset($result['error'])) {
            echo "Error: {$result['error']}\n";
        }
    }
}

echo "Sitemap regeneration completed.\n";
?>