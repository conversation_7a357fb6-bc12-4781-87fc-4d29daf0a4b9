<?php

function migrate_create_license_verification_tokens(PDO $pdo): bool
{
    $migration_name = 'create_license_verification_tokens';

    try {
        
        $check_sql = "SELECT 1 FROM migrations WHERE name = :name";
        $stmt_check = $pdo->prepare($check_sql);
        $stmt_check->execute([':name' => $migration_name]);
        if ($stmt_check->fetch()) {
            return true; 
        }

        $pdo->beginTransaction();

        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='license_verification_tokens';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            
            $pdo->exec("CREATE TABLE license_verification_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                verification_code TEXT NOT NULL,
                session_id TEXT,
                is_verified INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                expires_at TEXT NOT NULL,
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
            );");

            
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_license_id ON license_verification_tokens (license_id);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_verification_code ON license_verification_tokens (verification_code);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_session_id ON license_verification_tokens (session_id);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_expires_at ON license_verification_tokens (expires_at);");
        } else {
        }

        
        $log_sql = "INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))";
        $stmt_log = $pdo->prepare($log_sql);
        $stmt_log->execute([':name' => $migration_name]);

        $pdo->commit();
        return true;

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    require_once __DIR__ . '/../db.php'; 
    $pdo = get_db_connection();
    if ($pdo) {
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            executed_at TEXT NOT NULL
        )");
        
        if (migrate_create_license_verification_tokens($pdo)) {
            echo "Migration create_license_verification_tokens executed successfully.\n";
        } else {
            echo "Migration create_license_verification_tokens failed.\n";
        }
    } else {
        echo "Failed to connect to the database.\n";
    }
}
?>
