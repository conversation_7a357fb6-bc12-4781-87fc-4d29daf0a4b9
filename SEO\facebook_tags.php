<?php

$sitemapPath = '../sitemap.xml';

if (!file_exists($sitemapPath)) {
    die('Sitemap not found at: ' . $sitemapPath);
}

$xml = simplexml_load_file($sitemapPath);
if ($xml === false) {
    die('Failed to parse sitemap XML');
}

$urls = [];

foreach ($xml->url as $urlElement) {
    $loc = (string)$urlElement->loc;
    $lastmod = isset($urlElement->lastmod) ? (string)$urlElement->lastmod : '1970-01-01';
    
    $urls[] = [
        'url' => $loc,
        'lastmod' => $lastmod
    ];
}

usort($urls, function($a, $b) {
    return strtotime($b['lastmod']) - strtotime($a['lastmod']);
});

header('Content-Type: text/html; charset=utf-8');

echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Debug URLs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-bg: #1a1a2e;
            --card-bg: #16213e;
            --light-text: #e6e6e6;
        }
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--dark-bg);
            color: var(--light-text);
            line-height: 1.6;
            padding-top: 20px;
        }
        .container {
            max-width: 900px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            overflow: hidden;
            background-color: var(--card-bg);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .card-header {
            background-color: rgba(0,0,0,0.2);
            color: var(--secondary-color);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .list-group-item {
            background-color: transparent;
            border-color: rgba(255,255,255,0.1);
            color: var(--light-text);
        }
        .list-group-item a {
            color: var(--secondary-color);
            text-decoration: none;
        }
        .list-group-item a:hover {
            text-decoration: underline;
        }
        .btn-custom-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }
        .btn-custom-primary:hover {
            background-color: #2980b9; /* Darker shade of secondary-color */
            border-color: #2980b9;
        }
        .list-group-item {
            padding: 0.75rem 1.25rem;
            transition: background-color 0.2s ease-in-out;
        }
        .list-group-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .form-check-input {
            cursor: pointer;
            width: 1.3em;
            height: 1.3em;
            margin-top: 0.1em; /* Align better with text */
        }
        .form-check-input:checked {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        .debug-link {
            font-weight: 500;
        }
        .text-break {
            word-break: break-all;
        }
        .btn-outline-light {
            color: var(--light-text);
            border-color: var(--light-text);
        }
        .btn-outline-light:hover {
            color: var(--dark-bg);
            background-color: var(--light-text);
        }
        .card-header h1 {
            font-weight: 300; /* Lighter font for header */
        }
    </style>
</head>
<body>
<div class="container">
    <div class="card">
        <div class="card-header">
            <h1 class="h3 mb-0">Facebook Debug URLs</h1>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <button id="openSelectedBtn" class="btn btn-custom-primary"><i class="fas fa-external-link-alt"></i> Open Selected in New Tabs</button>
                <div>
                    <button id="selectAllBtn" class="btn btn-sm btn-outline-light me-2"><i class="fas fa-check-square"></i> Select All</button>
                    <button id="deselectAllBtn" class="btn btn-sm btn-outline-light"><i class="far fa-square"></i> Deselect All</button>
                </div>
            </div>
            <ul class="list-group list-group-flush" id="urlList">
';

foreach ($urls as $index => $urlData) {
    $encodedUrl = urlencode($urlData['url']);
    $debugUrl = "https://developers.facebook.com/tools/debug/?q={$encodedUrl}";
    $descriptiveText = htmlspecialchars($urlData['url']);
    $itemId = "url_checkbox_" . $index;
    echo "<li class='list-group-item d-flex align-items-center'>
            <div class='form-check me-3'>
                <input class='form-check-input url-checkbox' type='checkbox' value='{$debugUrl}' id='{$itemId}'>
                <label class='form-check-label' for='{$itemId}'></label>
            </div>
            <div class='flex-grow-1'>
                <a href='{$debugUrl}' target='_blank' class='debug-link text-break'>{$descriptiveText}</a>
                <small class='d-block text-muted'>Last Modified: {$urlData['lastmod']}</small>
            </div>
          </li>\n";
}

echo '            </ul>
        </div>
    </div>
</div>

<script>
    document.getElementById("openSelectedBtn").addEventListener("click", function() {
        const selectedCheckboxes = document.querySelectorAll("#urlList .url-checkbox:checked");
        if (selectedCheckboxes.length === 0) {
            alert("Please select at least one URL to open.");
            return;
        }
        selectedCheckboxes.forEach((checkbox, index) => {
            setTimeout(() => {
                window.open(checkbox.value, "_blank");
            }, index * 200); // 200ms delay between opening each tab
        });
    });

    document.getElementById("selectAllBtn").addEventListener("click", function() {
        document.querySelectorAll("#urlList .url-checkbox").forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    document.getElementById("deselectAllBtn").addEventListener("click", function() {
        document.querySelectorAll("#urlList .url-checkbox").forEach(checkbox => {
            checkbox.checked = false;
        });
    });
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
?>