<?php

$prefill_subject = '';
$prefill_message = '';
$product_ref = '';

if (isset($_GET['ref'])) {
    $product_ref = sanitize_input($_GET['ref']);
    $prefill_subject = "Pedido de informação sobre o produto: " . $product_ref;
}
if (isset($_GET['msg'])) {
    $prefill_message = sanitize_input($_GET['msg']);
} elseif (!empty($product_ref)) {
     $prefill_message = "Solicito mais informações sobre o produto com a referência " . $product_ref . ".";
}

$page_title = "Contacto"; 

$recaptcha_site_key = get_setting('recaptcha_site_key');
$recaptcha_enabled = get_setting('recaptcha_enabled', false);

$errors = $_SESSION['contact_form_errors'] ?? [];
$form_data = $_SESSION['contact_form_data'] ?? [];
unset($_SESSION['contact_form_errors'], $_SESSION['contact_form_data']);

if (!function_exists('display_error')) {
    function display_error($field, $errors) {
        if (isset($errors[$field])) {
            echo '<p class="text-red-400 text-xs mt-1">' . htmlspecialchars($errors[$field]) . '</p>';
        }
    }
}
if (!function_exists('old_value')) {
    function old_value($field, $form_data, $default = '') {
        return htmlspecialchars($form_data[$field] ?? $default);
    }
}
if (!function_exists('error_class')) {
     function error_class($field, $errors, $base_class) {
        return $base_class . (isset($errors[$field]) ? ' border-red-500 ring-1 ring-red-500' : ' border-gray-700');
    }
}

?>

<div class="container mx-auto text-center mb-12 px-4">
    <h1 class="text-3xl font-semibold mb-4"><?= $page_title ?></h1>
    <p class="text-gray-400">Tem alguma questão, dúvida ou sugestão? Gostaría de o ouvir! Envie-me uma mensagem e responderei assim que possível.</p>
</div>

<?php if (isset($errors['form'])): ?>
    <div class="container mx-auto bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">Erro!</strong>
        <span class="block sm:inline"><?= htmlspecialchars($errors['form']) ?></span>
    </div>
<?php endif; ?>

<div class="container mx-auto mb-12 px-4">
    <!-- Two-column layout for contact info and form -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
    <!-- Left Column: Contact Info & Social Media -->
    <div class="bg-gray-800 p-6 rounded-lg shadow-lg w-full">
        <h2 class="text-2xl font-semibold mb-6 text-white">Informações de Contacto</h2>

        <?php
        
        $contact_address = get_setting('contact_address');
        $contact_phone = get_setting('contact_phone');
        $contact_email = get_setting('contact_email');
        $contact_whatsapp = get_setting('contact_whatsapp');
        $contact_signal = get_setting('contact_signal');
        ?>

        <div class="grid gap-6 md:grid-cols-2">
            <?php if (!empty($contact_address)): ?>
            <div class="flex items-start bg-gray-700 p-4 rounded-lg">
                <div class="text-primary mr-3 mt-1"><i class="ri-map-pin-2-fill text-2xl"></i></div>
                <div>
                    <h3 class="text-white font-medium">Morada</h3>
                    <p class="text-gray-400"><?= nl2br(htmlspecialchars($contact_address)) ?></p>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($contact_phone)): ?>
            <div class="flex items-start bg-gray-700 p-4 rounded-lg">
                <div class="text-primary mr-3 mt-1"><i class="ri-phone-fill text-2xl"></i></div>
                <div>
                    <h3 class="text-white font-medium">Telefone</h3>
                    <p class="text-gray-400">
                        <a href="tel:<?= preg_replace('/[^0-9+]/', '', $contact_phone) ?>" class="hover:text-white transition">
                            <?= htmlspecialchars($contact_phone) ?>
                        </a>
                    </p>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($contact_email)): ?>
            <div class="flex items-start bg-gray-700 p-4 rounded-lg">
                <div class="text-primary mr-3 mt-1"><i class="ri-mail-fill text-2xl"></i></div>
                <div>
                    <h3 class="text-white font-medium">Email</h3>
                    <p class="text-gray-400">
                        <a href="mailto:<?= htmlspecialchars($contact_email) ?>" class="hover:text-white transition">
                            <?= htmlspecialchars($contact_email) ?>
                        </a>
                    </p>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($contact_whatsapp)): ?>
            <div class="flex items-start bg-gray-700 p-4 rounded-lg">
                <div class="text-primary mr-3 mt-1"><i class="ri-whatsapp-fill text-2xl"></i></div>
                <div>
                    <h3 class="text-white font-medium">WhatsApp</h3>
                    <p class="text-gray-400">
                        <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $contact_whatsapp) ?>" target="_blank" class="hover:text-white transition">
                            <?= htmlspecialchars($contact_whatsapp) ?>
                        </a>
                    </p>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($contact_signal)): ?>
            <div class="flex items-start bg-gray-700 p-4 rounded-lg">
                <div class="text-primary mr-3 mt-1"><i class="ri-chat-1-fill text-2xl"></i></div>
                <div>
                    <h3 class="text-white font-medium">Signal</h3>
                    <p class="text-gray-400">
                        <a href="https://signal.me/#p/<?= preg_replace('/[^0-9]/', '', $contact_signal) ?>" target="_blank" class="hover:text-white transition">
                            <?= htmlspecialchars($contact_signal) ?>
                        </a>
                    </p>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Social Media Links -->
        <h2 class="text-2xl font-semibold mt-8 mb-6 text-white">Redes Sociais</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <?php
            $social_links = [
                'facebook' => ['icon' => 'ri-facebook-fill', 'label' => 'Facebook', 'bg' => 'bg-blue-600'],
                'twitter' => ['icon' => 'ri-twitter-fill', 'label' => 'Twitter/X', 'bg' => 'bg-sky-500'],
                'instagram' => ['icon' => 'ri-instagram-fill', 'label' => 'Instagram', 'bg' => 'bg-pink-600'],
                'linkedin' => ['icon' => 'ri-linkedin-fill', 'label' => 'LinkedIn', 'bg' => 'bg-blue-700'],
                'youtube' => ['icon' => 'ri-youtube-fill', 'label' => 'YouTube', 'bg' => 'bg-red-600']
            ];

            $has_social_links = false;

            foreach ($social_links as $key => $info) {
                $url = get_setting('social_' . $key);
                if (!empty($url)) {
                    $has_social_links = true;
                    echo '<a href="' . htmlspecialchars($url) . '" target="_blank" rel="noopener noreferrer"
                             class="flex items-center justify-center ' . $info['bg'] . ' hover:opacity-90 text-white px-4 py-3 rounded-lg transition-colors">
                             <i class="' . $info['icon'] . ' mr-2 text-lg"></i> ' . $info['label'] . '
                          </a>';
                }
            }

            if (!$has_social_links) {
                echo '<p class="text-gray-400 col-span-full">Nenhuma rede social configurada.</p>';
            }
            ?>
        </div>
    </div>

    <!-- Right Column: Contact Form -->
    <div class="bg-gray-800 p-6 rounded-lg shadow-lg w-full">
        <h2 class="text-2xl font-semibold mb-6 text-white">Envie-me uma Mensagem</h2>
        <form action="index.php?action=submit_contact&<?= get_session_id_param() ?>" method="POST" id="contact-form" class="space-y-6">
         <?= csrf_input_field() ?>
        <input type="hidden" name="product_ref" value="<?= $product_ref ?>">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="contact_first_name" class="block text-sm font-medium text-gray-400 mb-1">Primeiro Nome <span class="text-red-400">*</span></label>
                <input type="text" id="contact_first_name" name="first_name" required value="<?= old_value('first_name', $form_data) ?>"
                       class="<?= error_class('first_name', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="Seu nome">
                 <?php display_error('first_name', $errors); ?>
            </div>
            <div>
                <label for="contact_last_name" class="block text-sm font-medium text-gray-400 mb-1">Apelido <span class="text-red-400">*</span></label>
                <input type="text" id="contact_last_name" name="last_name" required value="<?= old_value('last_name', $form_data) ?>"
                       class="<?= error_class('last_name', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="Seu apelido">
                 <?php display_error('last_name', $errors); ?>
            </div>
        </div>

        <div>
            <label for="contact_email" class="block text-sm font-medium text-gray-400 mb-1">Email <span class="text-red-400">*</span></label>
            <input type="email" id="contact_email" name="email" required value="<?= old_value('email', $form_data) ?>"
                   class="<?= error_class('email', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="<EMAIL>">
             <?php display_error('email', $errors); ?>
        </div>

         <div>
            <label for="contact_phone" class="block text-sm font-medium text-gray-400 mb-1">Telefone <span class="text-gray-500">(Opcional)</span></label>
            <input type="tel" id="contact_phone" name="phone" value="<?= old_value('phone', $form_data) ?>"
                   class="<?= error_class('phone', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="+351 ...">
             <?php display_error('phone', $errors); ?>
        </div>

        <div>
            <label for="contact_subject" class="block text-sm font-medium text-gray-400 mb-1">Assunto <span class="text-red-400">*</span></label>
            <input type="text" id="contact_subject" name="subject" required value="<?= old_value('subject', $form_data, $prefill_subject) ?>"
                   class="<?= error_class('subject', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>" placeholder="Como posso ajudar?">
             <?php display_error('subject', $errors); ?>
        </div>

        <div>
            <label for="contact_message" class="block text-sm font-medium text-gray-400 mb-1">Mensagem <span class="text-red-400">*</span></label>
            <textarea id="contact_message" name="message" rows="4" required
                      class="<?= error_class('message', $errors, 'w-full px-4 py-2 bg-gray-800 border rounded text-white focus:border-primary focus:ring-primary focus:outline-none') ?>"
                      placeholder="Sua mensagem..."><?= old_value('message', $form_data, $prefill_message) ?></textarea>
             <?php display_error('message', $errors); ?>
        </div>

        <div class="flex items-center mb-4">
            <input type="checkbox" id="send_copy" name="send_copy" value="1" class="w-4 h-4 text-primary bg-gray-800 border-gray-700 rounded focus:ring-primary focus:ring-1" <?= old_value('send_copy', $form_data) ? 'checked' : '' ?>>
            <label for="send_copy" class="ml-2 text-sm font-medium text-gray-300">Enviar-me uma cópia desta mensagem</label>
        </div>

        <?php if ($recaptcha_enabled && !empty($recaptcha_site_key)): ?>
            <div class="mb-3">
                 <div class="<?= error_class('g-recaptcha-response', $errors, 'g-recaptcha') ?>" data-sitekey="<?= $recaptcha_site_key ?>"></div>
                 <?php display_error('g-recaptcha-response', $errors); ?>
                 <script src="https://www.google.com/recaptcha/api.js" async defer></script>
            </div>
        <?php endif; ?>

        <div>
            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium whitespace-nowrap transition">
                Enviar Mensagem
            </button>
        </div>
    </form>
    </div>
    </div>
</div>

<?php

$map_latitude = get_setting('contact_map_latitude', '38.7223');
$map_longitude = get_setting('contact_map_longitude', '-9.1393');
$map_zoom = get_setting('contact_map_zoom', '15');

if (!empty($map_latitude) && !empty($map_longitude)):
?>
<!-- Map Section with Parallax Effect -->
<div class="container mx-auto px-4">
    <div class="relative w-full h-96 overflow-hidden mb-12 rounded-lg shadow-lg" id="contact-map-container">
        <div id="contact-map" class="absolute inset-0 w-full h-full z-10"></div>
    </div>
</div>

<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // Initialize map
    const map = L.map('contact-map').setView([<?= $map_latitude ?>, <?= $map_longitude ?>], <?= $map_zoom ?>);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Add marker at the specified location
    const marker = L.marker([<?= $map_latitude ?>, <?= $map_longitude ?>]).addTo(map);

    <?php if (!empty($contact_address)): ?>
    // Add popup with address if available
    marker.bindPopup("<?= addslashes(htmlspecialchars(str_replace("\n", '<br>', $contact_address))) ?>").openPopup();
    <?php endif; ?>

    // Parallax effect on scroll
    const mapContainer = document.getElementById('contact-map-container');
    const mapElement = document.getElementById('contact-map');

    if (mapContainer && mapElement) {
        window.addEventListener('scroll', () => {
            const rect = mapContainer.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                const scrollPercent = (window.innerHeight - rect.top) / (window.innerHeight + rect.height);
                const parallaxOffset = 50 * scrollPercent; // Adjust the 50 value to control parallax intensity
                mapElement.style.transform = `translateY(${parallaxOffset}px)`;
            }
        });

        // Make sure map resizes properly when window is resized
        window.addEventListener('resize', () => {
            map.invalidateSize();
        });
    }
});
</script>
<?php endif; ?>

<!-- Contact Form Specific JS -->
<script>
document.addEventListener('DOMContentLoaded', () => {
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            // Basic check if reCAPTCHA exists and is not checked (client-side hint)
            const recaptchaResponse = document.querySelector('[name="g-recaptcha-response"]');
            if (recaptchaResponse && recaptchaResponse.value === '') {
                 // You might want to display a more integrated error message
                 // alert('Por favor, complete o reCAPTCHA.');
                 // e.preventDefault(); // Prevent submission if reCAPTCHA is empty
                 // Server-side validation is still crucial
            }

            // Consider adding AJAX submission here instead of standard POST
            // to use the toast notifications from the footer.
            /*
            e.preventDefault();
            const formData = new FormData(contactForm);
            // Add action parameter if needed for AJAX handler
            // formData.append('action', 'submit_contact_ajax');

            fetch(contactForm.action, { // Use form's action URL
                method: 'POST',
                body: formData
            })
            .then(response => response.json()) // Assuming server responds with JSON
            .then(data => {
                if (data.success) {
                    showToast('Sucesso!', data.message || 'Mensagem enviada.');
                    contactForm.reset();
                    if (typeof grecaptcha !== 'undefined') { // Reset reCAPTCHA
                        grecaptcha.reset();
                    }
                } else {
                    // Display errors, potentially highlighting fields
                    showToast('Erro', data.error || 'Falha ao enviar mensagem.', 'ri-error-warning-line', 'border-red-500');
                    // You might need more sophisticated error display here based on data.errors object
                    if (data.errors && data.errors['g-recaptcha-response'] && typeof grecaptcha !== 'undefined') {
                         grecaptcha.reset(); // Reset captcha on error too
                    }
                }
            })
            .catch(error => {
                showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            });
            */
        });
    }
});
</script>