<?php

$post = $post ?? null;

if (!$post) {
    
    echo "<p class='text-red-500'>Erro: Post não encontrado.</p>";
    return;
}

$image_full_path = !empty($post['image_path']) ? PROJECT_ROOT . '/' . $post['image_path'] : null;
$image_url = ($image_full_path && file_exists($image_full_path))
             ? BASE_URL . '/' . $post['image_path'] . '?' . filemtime($image_full_path) 
             : null;

?>

<article class="max-w-4xl mx-auto bg-gray-800 rounded-lg shadow-lg overflow-hidden">
    <?php if ($image_url): ?>
        <div class="relative">
            <img src="<?php echo htmlspecialchars($image_url); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="w-full h-64 md:h-96 object-cover rounded-t-lg">
            <?php if (!empty($post['image_description'])): ?>
                <div class="absolute bottom-0 right-0 bg-black bg-opacity-60 text-white text-sm italic p-2 rounded-tl-md">
                    <?php echo htmlspecialchars($post['image_description']); ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="p-6 md:p-8 lg:p-10">
        <h1 class="text-3xl md:text-4xl font-bold mb-4 text-white"><?php echo htmlspecialchars($post['title']); ?></h1>

        <div class="text-sm text-gray-400 mb-6">
            <span>Publicado em <?php echo format_date($post['published_at']); ?></span>
            <?php if (!empty($post['categories'])):
                echo ' | Categorias: ';
                $cat_links = array_map(function($cat) {
                    $cat_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog&category=' . $cat['slug']);
                    return '<a href="' . $cat_url . '" class="hover:text-primary hover:underline">' . htmlspecialchars($cat['name']) . '</a>';
                }, $post['categories']);
                echo implode(', ', $cat_links);
            endif; ?>
        </div>

        <?php if ($post['post_type'] === 'article'): ?>
            <div class="prose prose-invert max-w-none text-gray-300 prose-a:text-primary hover:prose-a:text-primary-focus prose-strong:text-white">
                <?php
                    
                    echo nl2br($post['content']); 
                ?>
            </div>
        <?php elseif ($post['post_type'] === 'link'): ?>
            <!-- Link type notice -->
            <div class="mb-6 p-4 bg-gray-700 border-l-4 border-primary rounded-r-lg flex items-start">
                <div class="bg-primary bg-opacity-20 p-2 rounded-full mr-3">
                    <i class="ri-information-line text-primary text-xl"></i>
                </div>
                <div>
                    <p class="text-white font-medium">Este é um link externo</p>
                    <p class="text-gray-300 text-sm">Ao clicar no botão abaixo, você será redirecionado para um site externo em uma nova janela.</p>
                </div>
            </div>

            <?php if (!empty($post['link_description'])): ?>
                 <p class="text-gray-300 mb-6"><?php echo nl2br(htmlspecialchars($post['link_description'])); ?></p>
            <?php endif; ?>

            <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-6 py-3 bg-primary text-white font-semibold rounded-button hover:bg-secondary transition group">
                <span>Visitar Link</span>
                <i class="ri-external-link-line ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
            </a>
        <?php elseif ($post['post_type'] === 'CODE'): ?>
            <?php if (!empty($post['link_description'])): ?>
                 <p class="text-gray-300 mb-6"><?php echo nl2br(htmlspecialchars($post['link_description'])); ?></p>
            <?php endif; ?>
            <div class="code-execution-output mt-6">
                <?php
                if (isset($post['code_content']) && !empty($post['code_content'])) {
                    
                    
                    
                    try {
                        ob_start();
                        eval('?>' . $post['code_content']);
                        $output = ob_get_clean();
                        echo $output;
                    } catch (Throwable $t) {
                        
                        echo "<div class='p-4 bg-red-700 text-white rounded-md'>";
                        echo "<h5 class='font-bold'>Error during code execution:</h5>";
                        echo "<pre class='whitespace-pre-wrap break-all'>" . htmlspecialchars($t->getMessage()) . "</pre>";
                        echo "<p>File: " . htmlspecialchars($t->getFile()) . " on line " . htmlspecialchars($t->getLine()) . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<p class='text-gray-400'>Nenhum código para executar.</p>";
                }
                ?>
            </div>
        <?php endif; ?>

        <!-- Return Back Button and Categories Section -->
        <div class="mt-10 pt-6 border-t border-gray-700">
            <!-- Return Back Button -->
            <div class="mb-6">
                <button onclick="goBack()" class="inline-flex items-center px-4 py-2 bg-gray-700 text-white font-medium rounded-button hover:bg-gray-600 transition">
                    <i class="ri-arrow-left-line mr-2"></i> Voltar
                </button>
            </div>

            <!-- Categories List -->
            <?php if (!empty($active_categories)): ?>
            <div class="mt-6">
                <h3 class="text-xl font-semibold mb-4 text-white">Categorias do Blog</h3>
                <div class="flex flex-wrap gap-2">
                    <?php foreach ($active_categories as $category): ?>
                        <a href="<?php echo add_session_param_to_url(BASE_URL . '/index.php?view=blog&category=' . $category['slug']); ?>"
                           class="px-3 py-1 bg-gray-700 text-white rounded-full hover:bg-primary transition">
                            <?php echo htmlspecialchars($category['name']); ?>
                            <span class="text-xs ml-1">(<?php echo $category['post_count']; ?>)</span>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</article>

<!-- JavaScript for Back Button -->
<script>
function goBack() {
    // Try to go back in browser history
    if (window.history.length > 1) {
        window.history.back();
    } else {
        // Fallback to blog main page if no history
        window.location.href = '<?php echo add_session_param_to_url(BASE_URL . "/index.php?view=blog"); ?>';
    }
}
</script>

<!-- Add Tailwind Typography CDN or setup in tailwind.config.js -->
<script src="https://cdn.tailwindcss.com/3.4.1?plugins=typography"></script>