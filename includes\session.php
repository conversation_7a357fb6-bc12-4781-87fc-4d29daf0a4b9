<?php

if (!ob_get_level()) {
    ob_start();
}

if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.use_cookies', '0');
    ini_set('session.use_only_cookies', '0');
    ini_set('session.use_trans_sid', '0');
    ini_set('session.cache_limiter', 'nocache');
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php'; 

$pdo_for_session = null;

$current_user_fingerprint = null;

if (!defined('SESSION_UUID_HEADER')) {
    define('SESSION_UUID_HEADER', 'X-Session-UUID');
}
if (!defined('SESSION_ID_REGEX')) {
    define('SESSION_ID_REGEX', '/^[a-f0-9]{64}$/i');
}

function get_customer_ip(): string
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN';
    }
    
    if (strpos($ip, ',') !== false) {
        $ip = trim(explode(',', $ip)[0]);
    }
    return filter_var($ip, FILTER_VALIDATE_IP) ?: 'UNKNOWN';
}

function get_user_fingerprint(): string
{
    $ipAddress = get_customer_ip();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
    $acceptEncoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? ''; 
    
    $rawFingerprint = $ipAddress . '|' . $userAgent . '|' . $acceptLanguage . '|' . $acceptEncoding . '|' . SESSION_FINGERPRINT_SALT;
    return hash('sha256', $rawFingerprint);
}

function generate_session_id(): string
{
    try {
        return bin2hex(random_bytes(32));
    } catch (Exception $e) {
        
        
        return hash('sha256', uniqid(microtime() . SESSION_FINGERPRINT_SALT, true));
    }
}

function read_session(string $session_id): array|null|false
{
    global $current_user_fingerprint; 
    $pdo = get_db_connection();

    if (!$pdo) {
        
        return false; 
    }

    
    if (empty($current_user_fingerprint)) {
        
        
        $current_user_fingerprint = get_user_fingerprint();
        if (empty($current_user_fingerprint)) {
            
            return null; 
        }
    }

    try {
        
        $current_time_php = date('Y-m-d H:i:s');
        $stmt = $pdo->prepare(
            "SELECT data, user_fingerprint
             FROM sessions
             WHERE session_id = :id AND expires_at > :current_time"
        );
        $stmt->bindParam(':current_time', $current_time_php);
        $stmt->bindParam(':id', $session_id, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result === false) {
            
            if ($stmt->errorCode() === '00000') {
                
                
                return null; 
            } else {
                
                
                return false; 
            }
        }

        
        $stored_fingerprint = $result['user_fingerprint'] ?? null;

        if ($stored_fingerprint !== $current_user_fingerprint) {
            
            
            
            return null; 
        }

        
        return [
            'data' => $result['data'] ?? '',
            'fingerprint' => $stored_fingerprint 
        ];

    } catch (PDOException $e) {
        
        return false; 
    }
}

function write_session(string $session_id, string $data): bool
{
    global $pdo_for_session, $current_user_fingerprint;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
        if (!$pdo_for_session) {
            return false;
        }
    }

    if (empty($current_user_fingerprint)) {
        
        $current_user_fingerprint = get_user_fingerprint();
        if (empty($current_user_fingerprint)) {
             return false; 
        }
    }

    try {
        $last_access = date('Y-m-d H:i:s');
        $expires_at = date('Y-m-d H:i:s', time() + SESSION_LIFETIME_SECONDS);

        
        $sql = "INSERT OR REPLACE INTO sessions (session_id, data, user_fingerprint, created_at, last_access, expires_at, user_ip)
                VALUES (:id, :data, :fingerprint,
                        COALESCE((SELECT created_at FROM sessions WHERE session_id = :id), :created_at), -- Keep original created_at if exists
                        :last_access, :expires_at, :user_ip)";

        $stmt = $pdo_for_session->prepare($sql);
        $params = [
            ':id' => $session_id,
            ':data' => $data,
            ':fingerprint' => $current_user_fingerprint,
            ':created_at' => $last_access, 
            ':last_access' => $last_access,
            ':expires_at' => $expires_at,
            ':user_ip' => get_customer_ip()
        ];

        return $stmt->execute($params);

    } catch (PDOException $e) {
        return false;
    }
}

function destroy_session(string $session_id): bool
{
    global $pdo_for_session;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
         if (!$pdo_for_session) {
              
              return false;
         }
    }

    try {
        $stmt = $pdo_for_session->prepare("DELETE FROM sessions WHERE session_id = :id");
        $stmt->bindParam(':id', $session_id, PDO::PARAM_STR);
        return $stmt->execute();
    } catch (PDOException $e) {
        
        return false;
    }
}

function gc_session(int $max_lifetime_ignored): int|false
{
    global $pdo_for_session;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
         if (!$pdo_for_session) {
              
              return false;
         }
    }

    try {
        
        $current_time_php = date('Y-m-d H:i:s');
        $stmt = $pdo_for_session->prepare("DELETE FROM sessions WHERE expires_at <= :current_time");
        $stmt->bindParam(':current_time', $current_time_php);
        $stmt->execute();
        return $stmt->rowCount();
    } catch (PDOException $e) {
        
        return false;
    }
}

function open_session(string $savePath, string $sessionName): bool
{
    global $pdo_for_session;

     if (!$pdo_for_session) {
         $pdo_for_session = get_db_connection();
     }
     
     return ($pdo_for_session instanceof PDO);
}

function close_session(): bool
{
    global $pdo_for_session;
    
    
    
    
    
    return true;
}

function start_cookieless_session(): string
{
    global $pdo_for_session, $current_user_fingerprint;

    
    $pdo_for_session = get_db_connection();
    if (!$pdo_for_session) {
        
        
        die("Erro crítico: Não foi possível estabelecer ligação à base de dados para a sessão.");
    }

    
    // Only set session ini settings if no session is active
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.use_cookies', '0');
        ini_set('session.use_only_cookies', '0');
        ini_set('session.use_trans_sid', '0'); 
        ini_set('session.cache_limiter', 'nocache');
        ini_set('session.gc_probability', '1'); 
        ini_set('session.gc_divisor', '100');
        ini_set('session.gc_maxlifetime', SESSION_LIFETIME_SECONDS);
    }
    
    
    if (session_status() === PHP_SESSION_ACTIVE && ini_get('session.use_cookies') !== '0') {
        session_destroy();
    }

    
    if (session_status() === PHP_SESSION_NONE) {
        session_set_save_handler(
            'open_session',
            'close_session',
            function (string $id): string|false { 
                
                $session_info = read_session($id); 
                if ($session_info === false) {
                    
                    
                    
                    return "";
                }
                if ($session_info === null) {
                     
                     return ""; 
                }
                
                return $session_info['data'] ?? ''; 
            },
            'write_session', 
            'destroy_session',
            'gc_session'
        );
    }
    
    register_shutdown_function('session_write_close');

    

    
    
    $current_user_fingerprint = get_user_fingerprint();

    
    $session_id_to_use = null;
    $generate_new_session = true;
    $session_source = 'none'; 
    $log_reason = "";

    
    $uuid_header_key = 'HTTP_' . str_replace('-', '_', strtoupper(SESSION_UUID_HEADER));
    $uuid_from_header = $_SERVER[$uuid_header_key] ?? null;

    if ($uuid_from_header && preg_match(SESSION_ID_REGEX, $uuid_from_header)) {
        
        $session_info = read_session($uuid_from_header);

        if ($session_info === false) { 
            $log_reason = "DB error reading session via UUID header '$uuid_from_header'.";
        } elseif ($session_info === null) { 
            $log_reason = "Session ID from UUID header '$uuid_from_header' not found, expired, or fingerprint mismatch.";
        } else { 
            $session_id_to_use = $uuid_from_header;
            $generate_new_session = false;
            $session_source = 'uuid_header';
            $log_reason = "Session found via valid UUID header.";
        }
    } elseif ($uuid_from_header) {
        $log_reason = "Invalid UUID header format received: '$uuid_from_header'.";
    }

    
    if ($session_id_to_use === null) {
        $session_id_from_url = $_GET[SESSION_PARAM_NAME] ?? $_POST[SESSION_PARAM_NAME] ?? null;
        if ($session_id_from_url && preg_match(SESSION_ID_REGEX, $session_id_from_url)) {
            $session_info = read_session($session_id_from_url); 

            if ($session_info === false) { 
                $log_reason = "DB error reading session via URL param '$session_id_from_url'.";
            } elseif ($session_info === null) { 
                $log_reason = "Session ID from URL param '$session_id_from_url' not found, expired, or fingerprint mismatch.";
            } else { 
                $session_id_to_use = $session_id_from_url;
                $generate_new_session = false;
                $session_source = 'url_param';
                $log_reason = "Session found via URL parameter.";
            }
        } elseif ($session_id_from_url) {
            $log_reason = "Invalid Session ID format in URL parameter: '$session_id_from_url'.";
        }
    }

    
    
    if ($session_id_to_use === null) {
        if (empty($log_reason)) {
             $log_reason = "No valid session ID provided via header or URL.";
        }
        try {
            
            $current_time_php = date('Y-m-d H:i:s');
            $stmt_find = $pdo_for_session->prepare(
                "SELECT session_id FROM sessions
                 WHERE user_fingerprint = :fingerprint AND expires_at > :current_time
                 ORDER BY last_access DESC LIMIT 1"
            );
            $stmt_find->bindParam(':current_time', $current_time_php);
            $stmt_find->bindParam(':fingerprint', $current_user_fingerprint);
            $stmt_find->execute();
            $existing_session_id = $stmt_find->fetchColumn();

            if ($existing_session_id) {
                $session_id_to_use = $existing_session_id;
                $generate_new_session = false;
                $session_source = 'fingerprint';
                $log_reason .= " Reusing session found via fingerprint match.";
            } else {
                $log_reason .= " No existing session found for current fingerprint.";
            }
        } catch (PDOException $e) {
            
            $log_reason .= " DB error during fingerprint lookup.";
            
        }
    }

    
    if ($generate_new_session) {
        $session_id_to_use = generate_session_id(); 
        $session_source = 'new';
        $log_reason .= " Generating new session ID.";
    }

    
    

    
    if (session_status() !== PHP_SESSION_ACTIVE) {
        session_id($session_id_to_use);
    } else {
        
    }

    
    
    
    $session_started = false;
    if (session_status() === PHP_SESSION_ACTIVE) {
        $session_started = true;
    } else {
        try {
            $session_started = session_start(['read_and_close' => false]);
        } catch (Exception $e) {
            $session_started = false;
        }
    }

    if (!$session_started) {
        
        $_SESSION = [];
    }

    
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        
        if (!$generate_new_session && $session_source !== 'new') {
        } else {
        }
        $_SESSION['cart'] = [];
    } else {
        
        $i = 0;
        foreach ($_SESSION['cart'] as $key => $item) {
            if ($i < 3) { 

                $i++;
            } else {
                break;
            }
        }
    }
    

    
    if (!is_array($_SESSION['cart'])) {
        $_SESSION['cart'] = []; 
    }

    
    
    return $session_id_to_use;
}

function get_all_sessions_data(): array
{
    try {
        $pdo = get_db_connection(); 
        $sql = "SELECT session_id, data, created_at, last_access, expires_at, user_ip
                FROM sessions 
                WHERE expires_at > datetime('now')
                ORDER BY last_access DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        
        $ip_country_cache = [];
        
        $sessions_with_details = [];
        foreach ($sessions as $session) {
            $session_data = [];
            if (!empty($session['data'])) {
                
                $unserialized_data = @unserialize($session['data']);
                if ($unserialized_data !== false) {
                    $session_data = $unserialized_data;
                } else {
                    
                    if (strpos($session['data'], '|') !== false) {
                        $parts = explode('|', $session['data']);
                        for ($i = 0; $i < count($parts) - 1; $i += 2) {
                            $key = $parts[$i];
                            $value = isset($parts[$i + 1]) ? $parts[$i + 1] : '';
                            
                            
                            $unserialized_value = @unserialize($value);
                            if ($unserialized_value !== false) {
                                $session_data[$key] = $unserialized_value;
                            } else {
                                $session_data[$key] = $value;
                            }
                        }
                    }
                }
            }
            
            $cart_items = $session_data['cart'] ?? [];
            $cart_items_list = [];
            
            foreach ($cart_items as $item) {
                if (isset($item['name'])) {
                    $cart_items_list[] = $item['name'] . ' (Qty: ' . ($item['quantity'] ?? 1) . ')';
                }
            }
            
            
            $user_ip = $session['user_ip'] ?? 'N/A';
            if (!isset($ip_country_cache[$user_ip])) {
                $country_code = get_country_from_ip($user_ip);
                $country_name = get_country_name($country_code);
                $ip_country_cache[$user_ip] = [
                    'country_code' => $country_code,
                    'country_name' => $country_name
                ];
            } else {
                $country_code = $ip_country_cache[$user_ip]['country_code'];
                $country_name = $ip_country_cache[$user_ip]['country_name'];
            }
            
            $sessions_with_details[] = [
                'session_id' => $session['session_id'],
                'created_at' => $session['created_at'],
                'last_access' => $session['last_access'],
                'expires_at' => $session['expires_at'],
                'user_ip' => $user_ip,
                'country_code' => $country_code,
                'country_name' => $country_name,
                'cart_items_list' => $cart_items_list,
                'cart_count' => count($cart_items)
            ];
        }
        
        return [
            'success' => true,
            'sessions' => $sessions_with_details,
            'message' => 'Sessions loaded successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'sessions' => [],
            'message' => 'Error loading sessions: ' . $e->getMessage()
        ];
    }
}

function get_all_sessions_data_paginated(string $country_filter = '', string $ip_filter = '', string $sort = 'recent', int $page = 1, int $sessions_per_page = 20): array
{
    try {
        $pdo = get_db_connection();
        
        
        
        if (!empty($country_filter)) {
            
            $all_sessions_result = get_all_sessions_data();
            if (!$all_sessions_result['success']) {
                return $all_sessions_result;
            }
            
            $all_sessions = $all_sessions_result['sessions'];
            $filtered_sessions = [];
            
            
            foreach ($all_sessions as $session) {
                $matches_country = empty($country_filter) || 
                    stripos($session['country_name'], $country_filter) !== false || 
                    stripos($session['country_code'], $country_filter) !== false;
                
                $matches_ip = empty($ip_filter) || 
                    stripos($session['user_ip'], $ip_filter) !== false;
                
                if ($matches_country && $matches_ip) {
                    $filtered_sessions[] = $session;
                }
            }
            
            
            switch ($sort) {
                case 'oldest':
                    usort($filtered_sessions, function($a, $b) {
                        return strtotime($a['last_access']) - strtotime($b['last_access']);
                    });
                    break;
                case 'created_desc':
                    usort($filtered_sessions, function($a, $b) {
                        return strtotime($b['created_at']) - strtotime($a['created_at']);
                    });
                    break;
                case 'created_asc':
                    usort($filtered_sessions, function($a, $b) {
                        return strtotime($a['created_at']) - strtotime($b['created_at']);
                    });
                    break;
                case 'ip_asc':
                    usort($filtered_sessions, function($a, $b) {
                        return strcmp($a['user_ip'], $b['user_ip']);
                    });
                    break;
                case 'ip_desc':
                    usort($filtered_sessions, function($a, $b) {
                        return strcmp($b['user_ip'], $a['user_ip']);
                    });
                    break;
                case 'recent':
                default:
                    usort($filtered_sessions, function($a, $b) {
                        return strtotime($b['last_access']) - strtotime($a['last_access']);
                    });
                    break;
            }
            
            
            $total_filtered = count($filtered_sessions);
            $total_pages = max(1, ceil($total_filtered / $sessions_per_page));
            
            
            $offset = ($page - 1) * $sessions_per_page;
            $paginated_sessions = array_slice($filtered_sessions, $offset, $sessions_per_page);
            
            return [
                'success' => true,
                'sessions' => $paginated_sessions,
                'total' => $total_filtered,
                'total_pages' => $total_pages,
                'current_page' => $page,
                'sessions_per_page' => $sessions_per_page,
                'message' => 'Sessions loaded successfully'
            ];
        }
        
        
        $where_conditions = ["expires_at > datetime('now')"];
        $params = [];
        
        
        if (!empty($ip_filter)) {
            $where_conditions[] = "user_ip LIKE :ip_filter";
            $params[':ip_filter'] = '%' . $ip_filter . '%';
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        
        
        $order_clause = match($sort) {
            'recent' => 'ORDER BY last_access DESC',
            'oldest' => 'ORDER BY last_access ASC',
            'created_desc' => 'ORDER BY created_at DESC',
            'created_asc' => 'ORDER BY created_at ASC',
            'ip_asc' => 'ORDER BY user_ip ASC',
            'ip_desc' => 'ORDER BY user_ip DESC',
            default => 'ORDER BY last_access DESC'
        };
        
        
        $count_sql = "SELECT COUNT(*) FROM sessions $where_clause";
        $count_stmt = $pdo->prepare($count_sql);
        $count_stmt->execute($params);
        $total_filtered = $count_stmt->fetchColumn();
        
        
        $total_pages = max(1, ceil($total_filtered / $sessions_per_page));
        $offset = ($page - 1) * $sessions_per_page;
        
        
        $sql = "SELECT session_id, data, created_at, last_access, expires_at, user_ip
                FROM sessions 
                $where_clause
                $order_clause
                LIMIT :limit OFFSET :offset";
        
        $stmt = $pdo->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $sessions_per_page, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $sessions_with_details = [];
        
        
        $ip_country_cache = [];
        
        foreach ($sessions as $session) {
            $session_data = [];
            if (!empty($session['data'])) {
                
                $unserialized_data = @unserialize($session['data']);
                if ($unserialized_data !== false) {
                    $session_data = $unserialized_data;
                } else {
                    
                    if (strpos($session['data'], '|') !== false) {
                        $parts = explode('|', $session['data']);
                        for ($i = 0; $i < count($parts) - 1; $i += 2) {
                            $key = $parts[$i];
                            $value = isset($parts[$i + 1]) ? $parts[$i + 1] : '';
                            
                            
                            $unserialized_value = @unserialize($value);
                            if ($unserialized_value !== false) {
                                $session_data[$key] = $unserialized_value;
                            } else {
                                $session_data[$key] = $value;
                            }
                        }
                    }
                }
            }
            
            $cart_items = $session_data['cart'] ?? [];
            $cart_items_list = [];
            
            foreach ($cart_items as $item) {
                if (isset($item['name'])) {
                    $cart_items_list[] = $item['name'] . ' (Qty: ' . ($item['quantity'] ?? 1) . ')';
                }
            }
            
            
            $user_ip = $session['user_ip'] ?? 'N/A';
            if (!isset($ip_country_cache[$user_ip])) {
                $country_code = get_country_from_ip($user_ip);
                $country_name = get_country_name($country_code);
                $ip_country_cache[$user_ip] = [
                    'country_code' => $country_code,
                    'country_name' => $country_name
                ];
            } else {
                $country_code = $ip_country_cache[$user_ip]['country_code'];
                $country_name = $ip_country_cache[$user_ip]['country_name'];
            }
            
            $sessions_with_details[] = [
                'session_id' => $session['session_id'],
                'created_at' => $session['created_at'],
                'last_access' => $session['last_access'],
                'expires_at' => $session['expires_at'],
                'user_ip' => $user_ip,
                'country_code' => $country_code,
                'country_name' => $country_name,
                'cart_items_list' => $cart_items_list,
                'cart_count' => count($cart_items)
            ];
        }
        
        return [
            'success' => true,
            'sessions' => $sessions_with_details,
            'total' => $total_filtered,
            'total_pages' => $total_pages,
            'current_page' => $page,
            'sessions_per_page' => $sessions_per_page,
            'message' => 'Sessions loaded successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'sessions' => [],
            'total' => 0,
            'total_pages' => 1,
            'current_page' => 1,
            'sessions_per_page' => $sessions_per_page,
            'message' => 'Error loading sessions: ' . $e->getMessage()
        ];
    }
}

function delete_session_and_associated_tokens(string $session_id): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection failed'];
    }

    try {
        $pdo->beginTransaction();

        
        
        
        $stmt_download_tokens = $pdo->prepare("DELETE FROM download_tokens WHERE session_id = :session_id");
        $stmt_download_tokens->bindParam(':session_id', $session_id, PDO::PARAM_STR);
        $stmt_download_tokens->execute();
        $deleted_download_tokens = (int)$stmt_download_tokens->rowCount();
        
        $stmt_session = $pdo->prepare("DELETE FROM sessions WHERE session_id = :session_id");
        $stmt_session->bindParam(':session_id', $session_id, PDO::PARAM_STR);
        $stmt_session->execute();
        $deleted_session = (int)$stmt_session->rowCount();

        if ($deleted_session === 0) {
            $pdo->rollBack(); 
            return ['success' => false, 'message' => 'Session not found or already deleted.'];
        }

        $pdo->commit();

        $message = "Session '$session_id' deleted successfully.";
        if ($deleted_download_tokens > 0) {
            $message .= " $deleted_download_tokens download token(s) associated with this session were also deleted.";
        }

        return ['success' => true, 'message' => $message];
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Error deleting session: ' . $e->getMessage()];
    }
}
function get_session_id_param(bool $include_equals = true): string {
    $current_session_id = session_id(); 
    if (empty($current_session_id)) {
         
         return ''; 
    }
    
    $param_name = defined('SESSION_PARAM_NAME') ? SESSION_PARAM_NAME : 'sid';
    return $param_name . ($include_equals ? '=' . urlencode($current_session_id) : '');
}

function delete_expired_empty_sessions_action(): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Falha ao conectar à base de dados.'];
    }

    try {
        
        
        
        $current_time_php = date('Y-m-d H:i:s');
        $stmt_find_expired = $pdo->prepare(
            "SELECT session_id, data FROM sessions WHERE expires_at <= :current_time"
        );
        $stmt_find_expired->bindParam(':current_time', $current_time_php);
        $stmt_find_expired->execute();
        $expired_sessions = $stmt_find_expired->fetchAll(PDO::FETCH_ASSOC);

        $deleted_count = 0;
        $sessions_to_delete_ids = [];

        foreach ($expired_sessions as $session) {
            $session_data = [];
            if (!empty($session['data'])) {
                
                $unserialized_data = @unserialize($session['data']);
                if ($unserialized_data !== false) {
                    $session_data = $unserialized_data;
                } else {
                    
                    if (strpos($session['data'], '|') !== false) {
                        $parts = explode('|', $session['data']);
                        for ($i = 0; $i < count($parts) - 1; $i += 2) {
                            $key = $parts[$i];
                            $value = isset($parts[$i + 1]) ? $parts[$i + 1] : '';
                            
                            
                            $unserialized_value = @unserialize($value);
                            if ($unserialized_value !== false) {
                                $session_data[$key] = $unserialized_value;
                            } else {
                                $session_data[$key] = $value;
                            }
                        }
                    }
                }
            }

            
            if (empty($session_data['cart'])) {
                $sessions_to_delete_ids[] = $session['session_id'];
            }
        }

        if (!empty($sessions_to_delete_ids)) {
            
            $chunk_size = 100; 
            $chunks = array_chunk($sessions_to_delete_ids, $chunk_size);

            $pdo->beginTransaction();
            foreach ($chunks as $chunk) {
                if (empty($chunk)) continue;
                $placeholders = rtrim(str_repeat('?,', count($chunk)), ',');
                $stmt_delete = $pdo->prepare("DELETE FROM sessions WHERE session_id IN ($placeholders)");
                $stmt_delete->execute($chunk);
                $deleted_count += $stmt_delete->rowCount();
            }
            $pdo->commit();
        }

        if ($deleted_count > 0) {
            return ['success' => true, 'message' => "$deleted_count sessões expiradas e vazias foram eliminadas com sucesso."];
        } else {
            return ['success' => true, 'message' => 'Nenhuma sessão expirada e vazia encontrada para eliminar.'];
        }

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Erro ao eliminar sessões: ' . $e->getMessage()];
    }
}

function delete_non_portuguese_sessions_action(): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Falha ao conectar à base de dados.'];
    }

    try {
        
        $stmt = $pdo->prepare("SELECT session_id, user_ip FROM sessions WHERE user_ip IS NOT NULL AND user_ip != '' AND user_ip != 'UNKNOWN' AND user_ip != 'N/A' AND user_ip != 'LEGACY_SESSION'");
        $stmt->execute();
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $sessions_to_delete_ids = [];
        
        foreach ($sessions as $session) {
            $country_code = get_country_from_ip($session['user_ip']);
            
            if ($country_code !== 'pt') {
                $sessions_to_delete_ids[] = $session['session_id'];
            }
        }

        $deleted_count = 0;
        if (!empty($sessions_to_delete_ids)) {
            
            $chunk_size = 100;
            $chunks = array_chunk($sessions_to_delete_ids, $chunk_size);

            $pdo->beginTransaction();
            foreach ($chunks as $chunk) {
                if (empty($chunk)) continue;
                $placeholders = rtrim(str_repeat('?,', count($chunk)), ',');
                $stmt_delete = $pdo->prepare("DELETE FROM sessions WHERE session_id IN ($placeholders)");
                $stmt_delete->execute($chunk);
                $deleted_count += $stmt_delete->rowCount();
            }
            $pdo->commit();
        }

        if ($deleted_count > 0) {
            return ['success' => true, 'message' => "$deleted_count sessões de IPs externos a Portugal foram eliminadas com sucesso."];
        } else {
            return ['success' => true, 'message' => 'Nenhuma sessão de IP externo a Portugal encontrada para eliminar.'];
        }

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Erro ao eliminar sessões: ' . $e->getMessage()];
    }
}

?>
