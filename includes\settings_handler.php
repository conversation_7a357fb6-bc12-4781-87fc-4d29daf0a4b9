<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/security.php';

function handle_settings_update(array $post_data, array $files_data): array
{
$errors = [];
    $messages = [];
    $settings_to_update = [];
    $password_hash_to_update = null;
    $logo_action = 'none';
    $watermark_action = 'none';
    $logo_file_info = null;
    $watermark_file_info = null;

    $submitted_csrf = $post_data['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        $errors['csrf'] = 'Erro de segurança (CSRF). Tente novamente.';

        return ['success' => false, 'errors' => $errors, 'messages' => []];
    }

    $validation_rules = [

        'store_name'        => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => true, 'label' => 'Nome da Loja'],
        'admin_email'       => ['filter' => FILTER_VALIDATE_EMAIL, 'required' => true, 'label' => 'Email do Admin'],
        'store_description' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Descrição da Loja'],
        'digital_license_text' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Texto de Licença para Produtos Digitais'],
        'footer_info_line1' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Informação Importante 1 (Rodapé)'],
        'footer_info_line2' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Informação Importante 2 (Rodapé)'],
        'items_per_page'    => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => true, 'label' => 'Itens por Página'],
        'contact_address'   => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Morada de Contacto'],
        'contact_phone'     => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Telefone de Contacto'],
        'contact_email'     => ['filter' => FILTER_VALIDATE_EMAIL, 'required' => false, 'label' => 'Email de Contacto'],
        'contact_whatsapp'  => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'WhatsApp'],
        'contact_signal'    => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Signal'],
        'contact_map_latitude' => ['filter' => FILTER_VALIDATE_FLOAT, 'required' => false, 'label' => 'Latitude do Mapa'],
        'contact_map_longitude' => ['filter' => FILTER_VALIDATE_FLOAT, 'required' => false, 'label' => 'Longitude do Mapa'],
        'contact_map_zoom'  => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1, 'max_range' => 20], 'required' => false, 'label' => 'Zoom do Mapa'],
        'product_shipping_info' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Info Envios (Produto)'],
        'product_return_policy' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Política Devolução (Produto)'],
        'social_facebook'   => ['filter' => FILTER_VALIDATE_URL, 'required' => false, 'label' => 'Facebook URL'],
        'social_twitter'    => ['filter' => FILTER_VALIDATE_URL, 'required' => false, 'label' => 'Twitter/X URL'],
        'social_instagram'  => ['filter' => FILTER_VALIDATE_URL, 'required' => false, 'label' => 'Instagram URL'],
        'social_linkedin'   => ['filter' => FILTER_VALIDATE_URL, 'required' => false, 'label' => 'LinkedIn URL'],
        'social_youtube'    => ['filter' => FILTER_VALIDATE_URL, 'required' => false, 'label' => 'YouTube URL'],

        'default_currency'  => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => true, 'label' => 'Moeda Padrão'],
        'currency_symbol'   => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => true, 'label' => 'Símbolo Moeda'],
        'tax_rate_percent'  => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Taxa Imposto (%)'],
        'shipping_cost_flat'=> ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Custo Fixo Envio'],
        'min_order_value'   => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Valor Mínimo para Checkout'],
        'free_shipping_threshold' => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Valor Mínimo para Envio Gratuito'],
        'fixed_shipping_cost' => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Custo de Envio Fixo (Não-Digitais)'],
        'vat_id_threshold'  => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0], 'required' => false, 'label' => 'Limite Isenção NIF'],
        'vat_id_required_enabled' => ['filter' => FILTER_VALIDATE_BOOLEAN, 'flags' => FILTER_NULL_ON_FAILURE, 'required' => false, 'label' => 'Obrigar NIF Acima Limite'],

        'blog_posts_per_page' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => false, 'label' => 'Posts por Página (Blog)'],
        'blog_slider_count' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => false, 'label' => 'Número de Posts no Slider'],
        'blog_slider_delay' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => false, 'label' => 'Tempo de Pausa do Slider (segundos)'],

        'watermark_enabled' => ['filter' => FILTER_VALIDATE_BOOLEAN, 'flags' => FILTER_NULL_ON_FAILURE, 'required' => false, 'label' => 'Ativar Marca D\'água'],
        'watermark_opacity' => ['filter' => FILTER_VALIDATE_FLOAT, 'options' => ['min_range' => 0.0, 'max_range' => 1.0], 'required' => false, 'label' => 'Opacidade Marca D\'água'],
        'watermark_position'=> ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Posição Marca D\'água', 'allowed_values' => ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center']],
        'watermark_size_percent' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1, 'max_range' => 100], 'required' => false, 'label' => 'Tamanho Marca D\'água (%)'],

        'sitemap_default_directory' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Diretório Padrão para Sitemaps'],
        'keyword_cloud_max_keywords' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => true, 'label' => 'Número Máximo de Palavras-chave na Cloud (Produtos)'],
        'page_keyword_cloud_max_keywords' => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1], 'required' => false, 'label' => 'Número Máximo de Palavras-chave na Cloud (Páginas)'],

        'smtp_host'         => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'SMTP Host'],
        'smtp_port'         => ['filter' => FILTER_VALIDATE_INT, 'options' => ['min_range' => 1, 'max_range' => 65535], 'required' => false, 'label' => 'SMTP Porta'],
        'smtp_username'     => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'SMTP Username'],
        'smtp_secure'       => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'SMTP Segurança', 'allowed_values' => ['', 'ssl', 'tls']],
        'from_email'        => ['filter' => FILTER_VALIDATE_EMAIL, 'required' => false, 'label' => 'Email Remetente'],
        'from_name'         => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Nome Remetente'],
        'reply_to_email'    => ['filter' => FILTER_VALIDATE_EMAIL, 'required' => false, 'label' => 'Email Resposta'],
        'reply_to_name'     => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Nome Resposta'],

        'email_template_order_created' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Encomenda Criada'],
        'email_template_order_status_change' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Alteração de Estado'],
        'email_template_order_tracking' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Atualização de Rastreio'],
        'email_template_order_anonymized' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Dados Anonimizados'],
        'email_template_license_activated' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Licença Ativada'],
        'email_template_license_deactivated' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Licença Desativada'],
        'email_template_download_instructions' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Instruções de Download'],
        'email_template_verification_code' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Código de Verificação'],
        'license_verification_email_template' => ['filter' => FILTER_UNSAFE_RAW, 'required' => false, 'label' => 'Template Email: Verificação de Licença'],

        'digital_terms_email_enabled' => ['filter' => FILTER_VALIDATE_BOOLEAN, 'flags' => FILTER_NULL_ON_FAILURE, 'required' => false, 'label' => 'Ativar Email de Termos Digitais'],
        'digital_terms_email_subject' => ['filter' => FILTER_SANITIZE_SPECIAL_CHARS, 'required' => false, 'label' => 'Assunto Email de Termos Digitais'],
        
    ];

    $settings_input = $post_data['settings'] ?? [];

    foreach ($validation_rules as $key => $rule) {
        $value = $settings_input[$key] ?? null;
        $label = $rule['label'] ?? $key;
        $is_required = $rule['required'] ?? false;

        if ($is_required && ($value === null || $value === '')) {
            $errors[$key] = "O campo '{$label}' é obrigatório.";
            continue;
        }

        if (!$is_required && ($value === null || $value === '')) {
             $settings_to_update[$key] = '';
             continue;
        }

        $validated_value = filter_var($value, $rule['filter'], $rule['options'] ?? 0);

        if ($validated_value === false) {
             $errors[$key] = "Valor inválido para '{$label}'.";
        }

        elseif (isset($rule['allowed_values']) && !in_array($validated_value, $rule['allowed_values'], true)) {
            $errors[$key] = "Valor inválido para '{$label}'. Permitidos: " . implode(', ', $rule['allowed_values']);
        } else {

            $settings_to_update[$key] = $validated_value;
        }
    }

    $boolean_settings_keys = [
        'vat_id_required_enabled',
        'watermark_enabled',
        'order_email_status_change_enabled',
        'order_email_tracking_update_enabled',
        'order_email_created_enabled',
        'order_email_anonymized_enabled',
        'digital_terms_email_enabled'

    ];

    $email_template_keys = [
        'order_email_template_status_change',
        'order_email_template_tracking_update',
        'order_email_template_anonymized',
        'email_template_license_activated',
        'email_template_license_deactivated',
        'email_template_download_instructions',
        'email_template_verification_code',
        'license_verification_email_template'
    ];

    $raw_settings_input = $post_data['settings'] ?? [];

    foreach ($boolean_settings_keys as $bool_key) {

        $is_checked = (isset($raw_settings_input[$bool_key]) && $raw_settings_input[$bool_key] === '1') ||
                      (isset($post_data[$bool_key]) && $post_data[$bool_key] === '1');
        $settings_to_update[$bool_key] = $is_checked ? '1' : '0';
    }

    foreach ($email_template_keys as $template_key) {
        if (isset($raw_settings_input[$template_key])) {

            $settings_to_update[$template_key] = $raw_settings_input[$template_key];
        }
    }

    $current_password = $post_data['current_password'] ?? '';
    $new_password = $post_data['new_password'] ?? '';
    $confirm_password = $post_data['confirm_password'] ?? '';

    if (!empty($current_password) || !empty($new_password) || !empty($confirm_password)) {
        if (empty($current_password)) {
            $errors['password'] = "Introduza a password atual para definir uma nova.";
        } elseif (empty($new_password)) {
             $errors['password'] = "Nova password não pode estar vazia.";
        } elseif ($new_password !== $confirm_password) {
            $errors['password'] = "A nova password e a confirmação não coincidem.";
        } else {
            $stored_hash = get_setting('admin_password_hash');
            if (!$stored_hash || !verify_password($current_password, $stored_hash)) {
                 $errors['password'] = "A password atual está incorreta.";
            } else {
                 $password_hash_to_update = hash_password($new_password);
                 if (!$password_hash_to_update) {
                      $errors['password'] = "Falha ao gerar o hash da nova password.";
                 }
            }
        }
    }

    $smtp_password = $post_data['settings']['smtp_password'] ?? '';
    if (isset($post_data['settings']['smtp_password']) && !empty($smtp_password)) {

        $settings_to_update['smtp_password'] = $smtp_password;
    }

    $remove_logo = isset($post_data['remove_store_logo']) && $post_data['remove_store_logo'] == '1';

    if ($remove_logo) {
        $logo_action = 'remove';
    } elseif (isset($files_data['store_logo']) && $files_data['store_logo']['error'] === UPLOAD_ERR_OK) {
        $logo_file_info = $files_data['store_logo'];
        $allowed_logo_types = ['image/png', 'image/jpeg', 'image/gif', 'image/svg+xml'];
        $max_logo_size = 2 * 1024 * 1024;

        if (!in_array($logo_file_info['type'], $allowed_logo_types)) {
            $errors['store_logo'] = "Tipo de ficheiro inválido para o logo. Permitidos: PNG, JPG, GIF, SVG.";
        } elseif ($logo_file_info['size'] > $max_logo_size) {
            $errors['store_logo'] = "Ficheiro do logo demasiado grande (Máx: 2 MB).";
        } else {
            $logo_action = 'upload';
        }
    } elseif (isset($files_data['store_logo']) && $files_data['store_logo']['error'] !== UPLOAD_ERR_NO_FILE) {
         $errors['store_logo'] = "Erro ao carregar o ficheiro do logo (Código: " . $files_data['store_logo']['error'] . ").";
    }

    if (isset($files_data['watermark_image']) && $files_data['watermark_image']['error'] === UPLOAD_ERR_OK) {
        $watermark_file_info = $files_data['watermark_image'];
        $allowed_watermark_types = ['image/png'];
        $max_watermark_size = 1 * 1024 * 1024;

        if (!in_array($watermark_file_info['type'], $allowed_watermark_types)) {
            $errors['watermark_image'] = "A imagem da marca d'água deve ser um ficheiro PNG.";
        } elseif ($watermark_file_info['size'] > $max_watermark_size) {
             $errors['watermark_image'] = "Ficheiro da marca d'água demasiado grande (Máx: 1 MB).";
        } else {
             $watermark_action = 'upload';
        }
    } elseif (isset($files_data['watermark_image']) && $files_data['watermark_image']['error'] !== UPLOAD_ERR_NO_FILE) {
         $errors['watermark_image'] = "Erro ao carregar a imagem da marca d'água (Código: " . $files_data['watermark_image']['error'] . ").";
    }

    if (empty($errors)) {
        $pdo = get_db_connection();
        if (!$pdo) {
             $errors['database'] = "Falha na ligação à base de dados.";
        } else {
            $db_update_success = true;
            $file_op_success = true;
            $old_logo_path = null;
            $new_logo_path = null;
            $logo_op_performed = false;
            $watermark_op_performed = false;

            try {
                $pdo->beginTransaction();

                foreach ($settings_to_update as $key => $value) {
                    if ($key === 'digital_terms_email_selected_pages') {
                        
                        continue;
                    }
                    if (!update_setting($key, $value)) {

                         throw new Exception("Falha ao atualizar a configuração: '" . htmlspecialchars($key) . "'");
                    }
                }

                
                $digital_terms_selected_pages = $settings_input['digital_terms_email_selected_pages'] ?? [];
                if (!is_array($digital_terms_selected_pages)) {
                    $digital_terms_selected_pages = []; 
                }
                
                $sanitized_digital_terms_selected_pages = array_map('intval', $digital_terms_selected_pages);

                $json_selected_pages = json_encode($sanitized_digital_terms_selected_pages);
                if ($json_selected_pages === false) {
                    throw new Exception("Falha ao codificar para JSON a seleção de páginas de termos digitais.");
                }
                if (!update_setting('digital_terms_email_selected_pages', $json_selected_pages)) {
                    throw new Exception("Falha ao atualizar a configuração: 'digital_terms_email_selected_pages'");
                }

                 if(!empty($settings_to_update) || isset($settings_input['digital_terms_email_selected_pages'])) { 
                     $messages[] = "Configurações gerais atualizadas.";
                 }

                if ($password_hash_to_update !== null) {
                     if (!update_setting('admin_password_hash', $password_hash_to_update)) {
                          throw new Exception("Falha ao atualizar a password de administrador.");
                     }
                     $messages[] = "Password de administrador alterada.";
                }

                $pdo->commit();
                $db_update_success = true;

                if ($logo_action === 'remove') {
                    $old_logo_path = get_setting('store_logo_path');
                    if (!empty($old_logo_path)) {
                        $old_logo_file_absolute = rtrim(PROJECT_ROOT, '/') . '/' . ltrim($old_logo_path, '/');
                        if (file_exists($old_logo_file_absolute)) {
                            if (!@unlink($old_logo_file_absolute)) {
                                $errors['store_logo'] = "Falha ao remover ficheiro do logo atual (verifique permissões). A configuração foi removida.";
                                $file_op_success = false;
                            }
                        }

                         if (!update_setting('store_logo_path', '')) {

                         } else {
                              $messages[] = "Logo da loja removido.";
                              $logo_op_performed = true;
                         }
                    }
                } elseif ($logo_action === 'upload' && $logo_file_info) {
                    $logo_upload_dir_relative = 'public/assets/images/logo';
                    $logo_upload_dir_absolute = rtrim(PROJECT_ROOT, '/') . '/' . $logo_upload_dir_relative;
                    if (!is_dir($logo_upload_dir_absolute)) {
                        if (!mkdir($logo_upload_dir_absolute, 0755, true)) {
                            $errors['store_logo'] = "Falha ao criar diretório para o logo.";
                            $file_op_success = false;
                        }
                    }

                    if ($file_op_success) {
                        $file_extension = strtolower(pathinfo($logo_file_info['name'], PATHINFO_EXTENSION));
                        $new_logo_filename = 'store_logo.' . $file_extension;
                        $new_logo_path_relative = $logo_upload_dir_relative . '/' . $new_logo_filename;
                        $target_file_absolute = $logo_upload_dir_absolute . '/' . $new_logo_filename;
                        $old_logo_path = get_setting('store_logo_path');

                        if (move_uploaded_file($logo_file_info['tmp_name'], $target_file_absolute)) {
                             if (!update_setting('store_logo_path', $new_logo_path_relative)) {
                                 $errors['store_logo'] = "Logo carregado, mas falha ao guardar configuração na base de dados.";
                                 @unlink($target_file_absolute);
                                 $file_op_success = false;
                             } else {
                                 $messages[] = "Logo da loja carregado.";
                                 $logo_op_performed = true;

                                 if ($old_logo_path && $old_logo_path !== $new_logo_path_relative) {
                                     $old_logo_file_absolute = rtrim(PROJECT_ROOT, '/') . '/' . ltrim($old_logo_path, '/');
                                     if(file_exists($old_logo_file_absolute)) @unlink($old_logo_file_absolute);
                                 }
                             }
                        } else {
                             $errors['store_logo'] = "Falha ao mover o ficheiro do logo carregado.";
                             $file_op_success = false;
                        }
                    }
                }

                 if ($watermark_action === 'upload' && $watermark_file_info && $file_op_success) {
                     $upload_dir = dirname(WATERMARK_IMAGE_PATH);
                     $target_file = WATERMARK_IMAGE_PATH;
                     if (!is_dir($upload_dir)) {
                         if (!mkdir($upload_dir, 0755, true)) {
                             $errors['watermark_image'] = "Falha ao criar o diretório para a marca d'água.";
                             $file_op_success = false;
                         }
                     }
                     if ($file_op_success) {
                          if (move_uploaded_file($watermark_file_info['tmp_name'], $target_file)) {
                              $messages[] = "Imagem da marca d'água carregada.";
                              $watermark_op_performed = true;
                          } else {
                              $errors['watermark_image'] = "Falha ao carregar a imagem da marca d'água.";
                              $file_op_success = false;
                          }
                     }
                 }

                if ($db_update_success && !$file_op_success) {
                    $errors['file_operations'] = "Configurações da base de dados guardadas, mas ocorreu um erro nas operações com ficheiros (logo/marca d'água). Verifique os logs e permissões.";
                } elseif (empty($messages) && !$logo_op_performed && !$watermark_op_performed && !$password_hash_to_update && empty($settings_to_update)) {

                     $messages[] = "Nenhuma alteração detetada.";
                }

            } catch (Exception $e) {

                 if ($pdo->inTransaction()) {
                     $pdo->rollBack();
                 }
                 $errors['database'] = "Ocorreu um erro ao guardar as configurações: " . $e->getMessage();
            }
        }
    }

    unset($_SESSION['csrf_token']);
    generate_csrf_token();

    return [
        'success' => empty($errors),
        'errors' => $errors,
        'messages' => $messages
    ];
}