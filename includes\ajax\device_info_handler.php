<?php

if (session_status() === PHP_SESSION_NONE) {
    
require_once __DIR__ . '/../session.php';
$current_session_id = start_cookieless_session();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit('Method not allowed');
}

$orientation = $_POST['orientation'] ?? null;
$screen_width = $_POST['screen_width'] ?? null;
$screen_height = $_POST['screen_height'] ?? null;
$device_pixel_ratio = $_POST['device_pixel_ratio'] ?? null;

if ($orientation && in_array($orientation, ['portrait', 'landscape'])) {
    $_SESSION['device_orientation'] = $orientation;
}

if ($screen_width && is_numeric($screen_width) && $screen_width > 0) {
    $_SESSION['screen_width'] = (int)$screen_width;
}

if ($screen_height && is_numeric($screen_height) && $screen_height > 0) {
    $_SESSION['screen_height'] = (int)$screen_height;
}

if ($device_pixel_ratio && is_numeric($device_pixel_ratio) && $device_pixel_ratio > 0) {
    $_SESSION['device_pixel_ratio'] = (float)$device_pixel_ratio;
}

$_SESSION['device_info_updated'] = time();

http_response_code(200);
echo json_encode([
    'status' => 'success',
    'message' => 'Device information updated',
    'data' => [
        'orientation' => $_SESSION['device_orientation'] ?? null,
        'screen_width' => $_SESSION['screen_width'] ?? null,
        'screen_height' => $_SESSION['screen_height'] ?? null,
        'device_pixel_ratio' => $_SESSION['device_pixel_ratio'] ?? null
    ]
]);