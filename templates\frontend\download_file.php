<?php

if (session_status() === PHP_SESSION_ACTIVE) {
    session_write_close();
}

if (isset($_GET[SESSION_PARAM_NAME])) {
    $sid = sanitize_input($_GET[SESSION_PARAM_NAME]);
    session_id($sid);
} else {
}

require_once __DIR__ . '/../../includes/session.php';
$current_session_id = start_cookieless_session();

if (!isset($_SESSION['download_file']) ||
    !isset($_SESSION['download_file']['path']) ||
    !isset($_SESSION['download_file']['name']) ||
    !isset($_SESSION['download_file']['expires'])) {

    
    if (!isset($_SESSION['download_file'])) {
    } else {

    }

    
    header('Location: ' . add_session_param_to_url(BASE_URL . '/index.php?view=download&error=invalid_session'));
    exit;
}

if ($_SESSION['download_file']['expires'] < time()) {
    
    unset($_SESSION['download_file']);

    
    header('Location: ' . add_session_param_to_url(BASE_URL . '/index.php?view=download&error=expired'));
    exit;
}

$file_path = $_SESSION['download_file']['path'];
$file_name = $_SESSION['download_file']['name'];

if (!file_exists($file_path)) {
    
    unset($_SESSION['download_file']);

    
    header('Location: ' . add_session_param_to_url(BASE_URL . '/index.php?view=download&error=file_not_found'));
    exit;
}

$file_size = filesize($file_path);

if (!headers_sent()) {
    
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    
    $content_types = [
        'pdf' => 'application/pdf',
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        'exe' => 'application/octet-stream',
        'msi' => 'application/octet-stream',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'mp3' => 'audio/mpeg',
        'mp4' => 'video/mp4',
        'webm' => 'video/webm',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt' => 'application/vnd.ms-powerpoint',
        'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'txt' => 'text/plain',
        'csv' => 'text/csv',
        'html' => 'text/html',
        'htm' => 'text/html',
        'xml' => 'application/xml',
        'json' => 'application/json',
        'svg' => 'image/svg+xml',
        'ai' => 'application/postscript',
        'eps' => 'application/postscript',
        'psd' => 'application/octet-stream',
        'ttf' => 'font/ttf',
        'otf' => 'font/otf',
        'woff' => 'font/woff',
        'woff2' => 'font/woff2',
        'eot' => 'application/vnd.ms-fontobject',
        'sfnt' => 'font/sfnt'
    ];

    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';

    
    header('Content-Description: File Transfer');
    header('Content-Type: ' . $content_type);
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    header('Content-Length: ' . $file_size);

    
    while (ob_get_level()) {
        ob_end_clean();
    }

    
    if (file_exists($file_path) && is_readable($file_path)) {
        

        
        if (function_exists('readfile_chunked')) {
            readfile_chunked($file_path);
        } else {
            
            readfile($file_path);
        }

        

        
        if (strpos($file_path, 'temp') !== false || strpos($file_path, 'tmp') !== false) {
            if (@unlink($file_path)) {
            } else {
            }
        }

        
        if (session_status() === PHP_SESSION_ACTIVE) {
            unset($_SESSION['download_file']);
            session_write_close();
        }

        
        exit;
    } else {
        
        header('Location: ' . add_session_param_to_url(BASE_URL . '/index.php?view=download&error=file_not_found'));
        exit;
    }
} else {
    
    echo '<script>window.location.href="' . add_session_param_to_url(BASE_URL . '/index.php?view=download&error=headers_sent') . '";</script>';
    exit;
}
