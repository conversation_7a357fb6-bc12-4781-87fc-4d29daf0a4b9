

(function() {
    'use strict';

    
    window.dashboardInitialized = false;

    
    window.initDashboardButtons = function() {

        
        document.querySelectorAll('.widget-collapse-icon').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                
                const widget = this.closest('.dashboard-widget');
                if (!widget) {
                    return false;
                }

                const body = widget.querySelector('.widget-body');
                if (!body) {
                    return false;
                }

                
                if (body.style.display === 'none') {
                    body.style.display = 'block';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-up';
                    }
                } else {
                    body.style.display = 'none';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                    }
                }

                return false;
            };
        });

        
        document.querySelectorAll('.refresh-widget').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const widgetId = this.getAttribute('data-widget');

                
                const widget = document.getElementById(widgetId);
                if (widget) {
                    const widgetBody = widget.querySelector('.widget-body');
                    if (widgetBody) {
                        widgetBody.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div></div>';

                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                }

                return false;
            };
        });
    };

    
    window.initDashboard = function() {
        
        
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, retrying dashboard initialization...');
            setTimeout(() => {
                window.initDashboard();
            }, 200);
            return;
        }

        
        if (window.salesChart instanceof Chart) {
            window.salesChart.destroy();
            window.salesChart = null;
        } else if (window.salesChart) {
            console.warn('window.salesChart was defined but not a Chart instance. Clearing.');
            window.salesChart = null;
        }

        if (window.orderStatusChart instanceof Chart) {
            window.orderStatusChart.destroy();
            window.orderStatusChart = null;
        } else if (window.orderStatusChart) {
            console.warn('window.orderStatusChart was defined but not a Chart instance. Clearing.');
            window.orderStatusChart = null;
        }

        if (window.productCategoryChart instanceof Chart) {
            window.productCategoryChart.destroy();
            window.productCategoryChart = null;
        } else if (window.productCategoryChart) {
            console.warn('window.productCategoryChart was defined but not a Chart instance. Clearing.');
            window.productCategoryChart = null;
        }
        initDashboardCore();
        window.dashboardInitialized = true;

        
        window.initDashboardButtons();
    };

    
    function initDashboardCore() {
        initCharts();
        initCollapsibleWidgets();
        initDateRangeSelector();
        initQuickActions();
        setupRefreshButtons();
    }

    
    function initCharts() {
        try {
            initSalesChart();
            initOrderStatusChart();
            initProductCategoryChart();
        } catch (error) {
        }
    }

    
    function refreshCharts() {
        
        if (window.salesChart) window.salesChart.destroy();
        if (window.orderStatusChart) window.orderStatusChart.destroy();
        if (window.productCategoryChart) window.productCategoryChart.destroy();

        
        initCharts();
    }

    
    function initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) {
            console.warn('Sales chart canvas not found');
            return;
        }

        
        if (window.salesChart instanceof Chart) {
            window.salesChart.destroy();
        }
        
        const salesDataRaw = ctx.getAttribute('data-sales') || '[]';
        
        const salesData = JSON.parse(salesDataRaw);
        
        if (!salesData.length) {
            console.warn('No sales data available for chart');
            return;
        }

        
        const labels = salesData.map(item => item.date);
        const data = salesData.map(item => item.amount);

        
        try {
            window.salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: salesData.map(item => {
                        const date = new Date(item.date);
                        return date.toLocaleDateString('pt-PT', { day: '2-digit', month: '2-digit' });
                    }),
                    datasets: [{
                        label: 'Vendas (€)',
                        data: salesData.map(item => parseFloat(item.amount)),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' €';
                                }
                            }
                        }
                    }
                }
            });
        } catch (error) {
        }
    }

    
    function initOrderStatusChart() {
        const ctx = document.getElementById('orderStatusChart');
        if (!ctx) {
            console.warn('Order status chart canvas not found');
            return;
        }

        
        if (window.orderStatusChart instanceof Chart) {
            window.orderStatusChart.destroy();
        }
        
        const statusData = JSON.parse(ctx.getAttribute('data-status') || '[]');
        if (!statusData.length) {
            console.warn('No order status data available for chart');
            return;
        }

        
        const labels = statusData.map(item => item.status);
        const data = statusData.map(item => item.count);

        
        const backgroundColors = [
            'rgba(255, 193, 7, 0.9)',
            'rgba(13, 110, 253, 0.9)',
            'rgba(25, 135, 84, 0.9)',
            'rgba(220, 53, 69, 0.9)',
            'rgba(173, 181, 189, 0.9)'
        ];

        
        window.orderStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#f8f9fa', 
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(33, 37, 41, 0.9)', 
                        titleColor: '#f8f9fa', 
                        bodyColor: '#e9ecef' 
                    }
                }
            }
        });
    }

    
    function initProductCategoryChart() {
        const ctx = document.getElementById('productCategoryChart');
        if (!ctx) {
            console.warn('Product category chart canvas not found');
            return;
        }

        
        if (window.productCategoryChart instanceof Chart) {
            window.productCategoryChart.destroy();
        }
        
        const categoryData = JSON.parse(ctx.getAttribute('data-categories') || '[]');
        if (!categoryData.length) {
            console.warn('No product category data available for chart');
            return;
        }

        
        const labels = categoryData.map(item => item.name);
        const data = categoryData.map(item => item.count);

        
        window.productCategoryChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Produtos',
                    data: data,
                    backgroundColor: 'rgba(32, 201, 151, 0.8)', 
                    borderColor: 'rgba(32, 201, 151, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(33, 37, 41, 0.9)', 
                        titleColor: '#f8f9fa', 
                        bodyColor: '#e9ecef' 
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            precision: 0,
                            color: '#adb5bd' 
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            color: '#adb5bd' 
                        }
                    }
                }
            }
        });
    }

    
    function initCollapsibleWidgets() {

        
        const collapseButtons = document.querySelectorAll('.widget-collapse-icon');

        collapseButtons.forEach(button => {
            
            const newButton = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newButton, button);
            }
        });

        
        document.querySelectorAll('.widget-collapse-icon').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                
                const widget = this.closest('.dashboard-widget');
                if (!widget) {
                    return;
                }

                const body = widget.querySelector('.widget-body');
                if (!body) {
                    return;
                }

                
                if (body.style.display === 'none') {
                    body.style.display = 'block';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-up';
                    }
                } else {
                    body.style.display = 'none';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                    }
                }

                return false; 
            };
        });

        
        setupRefreshButtons();
    }

    
    function initDateRangeSelector() {
        const selector = document.getElementById('dashboardDateRange');
        if (!selector) return;

        selector.addEventListener('change', function() {
            const form = this.closest('form');
            if (form) form.submit();
        });
    }

    
    function initQuickActions() {
        
    }

    
    function setupRefreshButtons() {

        
        const refreshButtons = document.querySelectorAll('.refresh-widget');

        refreshButtons.forEach(button => {
            
            const newButton = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newButton, button);
            }
        });

        
        document.querySelectorAll('.refresh-widget').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const widgetId = this.getAttribute('data-widget');
                refreshWidget(widgetId);

                return false; 
            };
        });
    }

    
    function refreshWidget(widgetId) {
        
        const widget = document.getElementById(widgetId);
        if (!widget) return;

        const widgetBody = widget.querySelector('.widget-body');
        if (widgetBody) {
            widgetBody.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div></div>';
        }

        
        
        setTimeout(() => {
            
            window.location.reload();
        }, 1000);
    }

    
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.dashboardInitialized) {
            initDashboardCore();
            window.dashboardInitialized = true;
        }

        
        window.initDashboardButtons();

        
        setupMutationObserver();
    });

    
    document.addEventListener('contentLoaded', function() {
        initDashboardCore();

        
        window.initDashboardButtons();
    });

    
    function setupMutationObserver() {

        
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    
                    let hasButtons = false;
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { 
                            if (node.querySelector('.widget-collapse-icon, .refresh-widget')) {
                                hasButtons = true;
                            }
                        }
                    });

                    
                    if (hasButtons) {
                        window.initDashboardButtons();
                    }
                }
            });
        });

        
        observer.observe(document.body, { childList: true, subtree: true });
    }
})();
