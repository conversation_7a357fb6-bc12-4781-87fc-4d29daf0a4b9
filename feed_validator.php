<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$atomFeedUrl = 'https://joaocesarsilva.com/atomfeed.xml';
$outputRssFile = 'feed_validated.xml';

function getElementValue($parentElement, $tagName, $namespaceURI = null) {
    if ($namespaceURI) {
        $elements = $parentElement->getElementsByTagNameNS($namespaceURI, $tagName);
    } else {
        $elements = $parentElement->getElementsByTagName($tagName);
    }
    if ($elements->length > 0 && $elements->item(0)) {
        return trim($elements->item(0)->nodeValue);
    }
    return '';
}

function formatDateForRss($dateString) {
    if (empty($dateString)) return '';
    try {
        $date = new DateTime($dateString);
        return $date->format(DateTime::RSS);
    } catch (Exception $e) {
        $timestamp = strtotime($dateString);
        if ($timestamp) {
            return date(DateTime::RSS, $timestamp);
        }
        return '';
    }
}

function formatDateForW3CDTF($dateString) {
    if (empty($dateString)) return '';
    try {
        $date = new DateTime($dateString);
        return $date->format(DateTime::W3C); 
    } catch (Exception $e) {
        $timestamp = strtotime($dateString);
        if ($timestamp) {
            return date(DateTime::W3C, $timestamp);
        }
        return '';
    }
}

function sanitize_for_xml($string) {
    if ($string === null || $string === '') return '';
    
    
    
    $decodedString = html_entity_decode($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');

    
    
    return htmlspecialchars($decodedString, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
}

$atomXmlContent = @file_get_contents($atomFeedUrl);
if ($atomXmlContent === false) {
    die("Error: Could not fetch the Atom feed. Check URL or network connection.\n");
}

$atomDoc = new DOMDocument();
libxml_use_internal_errors(true);
if (!$atomDoc->loadXML($atomXmlContent)) {
    $errors = libxml_get_errors();
    $errorMsg = "Error: Could not parse Atom XML.\n";
    foreach ($errors as $error) {
        $errorMsg .= "XML Error [{$error->level}] (Code {$error->code}) at line {$error->line}, col {$error->column}: " . trim($error->message) . "\n";
    }
    libxml_clear_errors();
    die($errorMsg);
}
libxml_use_internal_errors(false);

$atomNamespace = 'http://www.w3.org/2005/Atom';
$dcNamespace = 'http://purl.org/dc/elements/1.1/';

$rssDoc = new DOMDocument('1.0', 'UTF-8');
$rssDoc->formatOutput = true;

$rssRoot = $rssDoc->createElement('rss');
$rssRoot->setAttribute('version', '2.0');
$rssRoot->setAttributeNS('http://www.w3.org/2000/xmlns/', 'xmlns:dc', $dcNamespace);
$rssRoot->setAttributeNS('http://www.w3.org/2000/xmlns/', 'xmlns:atom', $atomNamespace); 
$rssDoc->appendChild($rssRoot);

$channelNode = $rssDoc->createElement('channel');
$rssRoot->appendChild($channelNode);

$feedTitle = getElementValue($atomDoc->documentElement, 'title', $atomNamespace);
$channelNode->appendChild($rssDoc->createElement('title', sanitize_for_xml($feedTitle)));

$atomLinks = $atomDoc->documentElement->getElementsByTagNameNS($atomNamespace, 'link');
$siteLink = '';
$selfLinkHref = ''; 

foreach ($atomLinks as $link) {
    if ($link->hasAttribute('rel') && $link->getAttribute('rel') == 'self') {
        $selfLinkHref = $link->getAttribute('href');
    }
    if (empty($siteLink) && (!$link->hasAttribute('rel') || ($link->getAttribute('rel') !== 'self' && $link->getAttribute('rel') !== 'enclosure'))) {
        $siteLink = $link->getAttribute('href');
    }
}
if (empty($siteLink) && $atomLinks->length > 0) { 
    $siteLink = $atomLinks->item(0)->getAttribute('href');
}
$channelNode->appendChild($rssDoc->createElement('link', sanitize_for_xml($siteLink)));

if (!empty($selfLinkHref)) {
    $atomSelfLinkNode = $rssDoc->createElementNS($atomNamespace, 'atom:link');
    $atomSelfLinkNode->setAttribute('href', sanitize_for_xml($selfLinkHref));
    $atomSelfLinkNode->setAttribute('rel', 'self');
    $atomSelfLinkNode->setAttribute('type', 'application/rss+xml');
    $channelNode->appendChild($atomSelfLinkNode);
}

$feedSubtitle = getElementValue($atomDoc->documentElement, 'subtitle', $atomNamespace);
$channelNode->appendChild($rssDoc->createElement('description', sanitize_for_xml($feedSubtitle)));

$channelNode->appendChild($rssDoc->createElement('language', 'pt-PT')); 

$feedUpdated = getElementValue($atomDoc->documentElement, 'updated', $atomNamespace);
$channelNode->appendChild($rssDoc->createElement('lastBuildDate', formatDateForRss($feedUpdated)));

$atomAuthor = $atomDoc->documentElement->getElementsByTagNameNS($atomNamespace, 'author')->item(0);
$channelAuthorEmail = '<EMAIL>'; 
$channelAuthorName = 'Joao Cesar Silva'; 

if ($atomAuthor) {
    $authorNameValue = getElementValue($atomAuthor, 'name', $atomNamespace);
    if ($authorNameValue) $channelAuthorName = $authorNameValue;
    
    
    
}

$channelNode->appendChild($rssDoc->createElement('managingEditor', sanitize_for_xml($channelAuthorEmail . ' (' . $channelAuthorName . ')')));
$channelNode->appendChild($rssDoc->createElement('webMaster', sanitize_for_xml($channelAuthorEmail . ' (' . $channelAuthorName . ')')));

$entries = $atomDoc->getElementsByTagNameNS($atomNamespace, 'entry');

foreach ($entries as $entry) {
    $itemNode = $rssDoc->createElement('item');

    $itemTitle = getElementValue($entry, 'title', $atomNamespace);
    if (empty($itemTitle)) {
        
        
        $entryLinkValue = '';
         $entryLinksList = $entry->getElementsByTagNameNS($atomNamespace, 'link');
         foreach($entryLinksList as $linkElItem) {
             if (!$linkElItem->hasAttribute('rel') || ($linkElItem->getAttribute('rel') !== 'enclosure' && $linkElItem->getAttribute('rel') !== 'edit')) {
                 $entryLinkValue = $linkElItem->getAttribute('href');
                 break;
             }
         }
        if (strpos($entryLinkValue, 'index.php?page=') !== false) {
            $itemTitle = "Page: " . basename(parse_url($entryLinkValue, PHP_URL_QUERY));
        } else {
            $itemTitle = '[No Title Provided]'; 
        }
    }
    $itemNode->appendChild($rssDoc->createElement('title', sanitize_for_xml($itemTitle)));

    $entryLink = '';
    $entryLinks = $entry->getElementsByTagNameNS($atomNamespace, 'link');
    foreach($entryLinks as $linkEl) {
        if (!$linkEl->hasAttribute('rel') || ($linkEl->getAttribute('rel') !== 'enclosure' && $linkEl->getAttribute('rel') !== 'edit')) {
            $entryLink = $linkEl->getAttribute('href');
            break;
        }
    }
    if (empty($entryLink) && $entryLinks->length > 0) {
        $entryLink = $entryLinks->item(0)->getAttribute('href');
    }

    if (!empty($entryLink)) {
        $itemNode->appendChild($rssDoc->createElement('link', sanitize_for_xml($entryLink)));
    } else {
        
        
    }

    $itemDescription = getElementValue($entry, 'summary', $atomNamespace);
    if (empty($itemDescription) || $itemDescription == 'N/A') { 
        $itemDescription = getElementValue($entry, 'content', $atomNamespace);
    }
    if (empty($itemDescription) || $itemDescription == 'N/A') { 
        $itemDescription = "No description available.";
    }

    
    $cleanedDescription = preg_replace('/\s+/', ' ', strip_tags($itemDescription));
    $itemNode->appendChild($rssDoc->createElement('description', sanitize_for_xml($cleanedDescription)));

    $datePublished = getElementValue($entry, 'published', $atomNamespace);
    if(!empty($datePublished)) {
        $itemNode->appendChild($rssDoc->createElement('pubDate', formatDateForRss($datePublished)));
    }

    $itemId = getElementValue($entry, 'id', $atomNamespace);
    $guidNode = $rssDoc->createElement('guid', sanitize_for_xml($itemId));
    
    $guidNode->setAttribute('isPermaLink', (empty($entryLink) && filter_var($itemId, FILTER_VALIDATE_URL)) ? 'true' : 'false');
    $itemNode->appendChild($guidNode);

    $itemAuthorName = $channelAuthorName; 
    $entryAuthorNode = $entry->getElementsByTagNameNS($atomNamespace, 'author')->item(0);
    if ($entryAuthorNode) {
        $specificAuthorName = getElementValue($entryAuthorNode, 'name', $atomNamespace);
        if ($specificAuthorName) $itemAuthorName = $specificAuthorName;
    }
    
    $itemNode->appendChild($rssDoc->createElementNS($dcNamespace, 'dc:creator', sanitize_for_xml($itemAuthorName)));

    $categories = $entry->getElementsByTagNameNS($atomNamespace, 'category');
    foreach ($categories as $category) {
        if ($category->hasAttribute('term')) {
            $term = $category->getAttribute('term');
            $itemNode->appendChild($rssDoc->createElement('category', sanitize_for_xml($term)));
        }
    }

    $enclosureUrl = null;
    $enclosures = $entry->getElementsByTagNameNS($atomNamespace, 'link');
    foreach ($enclosures as $enclosureLink) {
        if ($enclosureLink->hasAttribute('rel') && $enclosureLink->getAttribute('rel') == 'enclosure') {
            $enclosureUrl = $enclosureLink->getAttribute('href');
            $enclosureType = $enclosureLink->getAttribute('type');
            $enclosureLength = $enclosureLink->hasAttribute('length') ? $enclosureLink->getAttribute('length') : '0';

            if(!empty($enclosureUrl) && !empty($enclosureType)) {
                $enclosureNode = $rssDoc->createElement('enclosure');
                $enclosureNode->setAttribute('url', sanitize_for_xml($enclosureUrl));
                $enclosureNode->setAttribute('type', sanitize_for_xml($enclosureType));
                $enclosureNode->setAttribute('length', sanitize_for_xml($enclosureLength)); 
                $itemNode->appendChild($enclosureNode);
            }
            break;
        }
    }

    $dateUpdated = getElementValue($entry, 'updated', $atomNamespace);
    
    if(!empty($dateUpdated) && !empty($datePublished) && $dateUpdated !== $datePublished) {
        $itemNode->appendChild($rssDoc->createElementNS($dcNamespace, 'dc:date', formatDateForW3CDTF($dateUpdated)));
    }

    
    
    if (!empty($itemTitle) && (!empty($entryLink) || $guidNode->getAttribute('isPermaLink') == 'true')) {
        $channelNode->appendChild($itemNode);
    } else {
        
    }
}

if ($rssDoc->save($outputRssFile)) {
    
    
    if (php_sapi_name() !== 'cli') {
        header('Content-Type: application/rss+xml; charset=utf-8');
        echo $rssDoc->saveXML();
        exit;
    }
} else {
    echo "Error: Could not save the RSS feed.\n";
}

?>