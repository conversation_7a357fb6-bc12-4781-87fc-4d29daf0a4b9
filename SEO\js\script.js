
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]; 
    }
}

let urls = [];
let currentUrlIndex = 0;
let totalUrls = 0;
let isProcessing = false;
let shouldStop = false;
let backlinkParallelWorkers = 1; 
let urlLimit = 0; 
let extractedUrls = []; 

const stats = {
    urlsProcessed: 0,
    urlsSkipped: 0,
    successPings: 0,
    failedPings: 0
};

let pingSites = []; 
let pingSitesModified = false;
let nonResponsiveSites = [];
let failedLinks = []; 

window.addEventListener('load', () => {
    
    
    window.managePingSitesModal = new bootstrap.Modal(document.getElementById('managePingSitesModal'));
    window.addEditPingSiteModal = new bootstrap.Modal(document.getElementById('addEditPingSiteModal'));
    window.selectUrlsModal = new bootstrap.Modal(document.getElementById('selectUrlsModal'));
    window.confirmRemoveModal = new bootstrap.Modal(document.getElementById('confirmRemoveModal'));
    window.confirmRemoveAllNonResponsiveModal = new bootstrap.Modal(document.getElementById('confirmRemoveAllNonResponsiveModal'));

    
    const urlLimitSlider = document.getElementById('urlLimitSlider');
    const urlLimitValue = document.getElementById('urlLimitValue');
    if (urlLimitSlider && urlLimitValue) {
        urlLimitValue.textContent = urlLimitSlider.value === '0' ? 'No Limit' : urlLimitSlider.value;
        urlLimit = parseInt(urlLimitSlider.value);
        urlLimitSlider.addEventListener('input', (event) => {
            urlLimit = parseInt(event.target.value);
            urlLimitValue.textContent = urlLimit === 0 ? 'No Limit' : urlLimit;
            applyUrlLimit(); 
        });
    }

    
    loadPingSitesFromServer();

    
    updateStatsDisplay();
    document.getElementById('stopButton').disabled = true;
    document.getElementById('selectUrlsButton').disabled = false;
});

function addLog(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    if (!logContainer) return;
    const entry = document.createElement('div');
    entry.className = `log-entry log-${type}`;
    
    const textNode = document.createTextNode(`[${new Date().toLocaleTimeString()}] ${message}`);
    entry.appendChild(textNode);
    logContainer.appendChild(entry);
    logContainer.scrollTop = logContainer.scrollHeight; 
}

function updateProgress(current, total) {
    const progressBar = document.getElementById('progressBar');
    if (!progressBar) return;
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
    progressBar.setAttribute('aria-valuenow', percentage);
}

function updateStatsDisplay() {
    document.getElementById('urlsProcessed').textContent = stats.urlsProcessed;
    document.getElementById('urlsSkipped').textContent = stats.urlsSkipped;
    document.getElementById('successfulPings').textContent = stats.successPings;
    document.getElementById('failedPings').textContent = stats.failedPings;
}

function addResultItem(url, siteName, status, message = '') {
    const resultsContainer = document.getElementById('resultsContainer');
    if (!resultsContainer) return;

    if (resultsContainer.firstChild && resultsContainer.firstChild.textContent && resultsContainer.firstChild.textContent.includes('Results will appear here')) {
        resultsContainer.innerHTML = ''; 
    }
    const item = document.createElement('div');
    item.className = 'result-item list-group-item'; 
    item.innerHTML = `
        <div class="d-flex w-100 justify-content-between">
            <h5 class="mb-1">${status === 'success' ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>'} ${siteName}</h5>
            <small class="text-muted-light">${new Date().toLocaleTimeString()}</small>
        </div>
        <p class="mb-1">URL: <a href="${url}" target="_blank">${url}</a></p>
        ${message ? `<small class="text-muted-light"><em>${message}</em></small>` : ''}
    `;
    resultsContainer.insertBefore(item, resultsContainer.firstChild); 
}

function addFailedLink(url, siteName, error) {
    const failedLinksContainer = document.getElementById('failedLinksContainer');
    if (!failedLinksContainer) return;

    if (failedLinksContainer.firstChild && failedLinksContainer.firstChild.textContent && failedLinksContainer.firstChild.textContent.includes('Failed links will appear here')) {
        failedLinksContainer.innerHTML = ''; 
    }
    const item = document.createElement('div');
    item.className = 'failed-link-item list-group-item list-group-item-danger'; 
    item.innerHTML = `
        <div class="d-flex w-100 justify-content-between">
            <h5 class="mb-1"><i class="fas fa-exclamation-triangle"></i> ${siteName}</h5>
            <small class="text-muted-light">${new Date().toLocaleTimeString()}</small>
        </div>
        <p class="mb-1">URL: <a href="${url}" target="_blank">${url}</a></p>
        <small>Error: ${error}</small>
    `;
    failedLinksContainer.insertBefore(item, failedLinksContainer.firstChild);

    
    failedLinks.push({ url, siteName, error });
    const badge = document.getElementById('failed-tab-badge'); 
    if (badge) {
        badge.textContent = failedLinks.length;
        badge.style.display = failedLinks.length > 0 ? 'inline-block' : 'none';
    }
}

function addNonResponsiveSite(siteName, siteUrl) {
    if (!nonResponsiveSites.some(site => site.name === siteName)) {
        nonResponsiveSites.push({ name: siteName, url: siteUrl });
        renderNonResponsiveSitesList(); 
    }
}

function removeNonResponsiveSite(siteName) {
    nonResponsiveSites = nonResponsiveSites.filter(site => site.name !== siteName);
    renderNonResponsiveSitesList();
}

function confirmRemoveAllNonResponsive() {
    window.confirmRemoveAllNonResponsiveModal.show();
}

function removeAllNonResponsiveSites() {
    nonResponsiveSites = [];
    renderNonResponsiveSitesList();
    window.confirmRemoveAllNonResponsiveModal.hide();
}

function renderNonResponsiveSitesList() {
    const container = document.getElementById('nonResponsiveSitesContainer');
    if (!container) return;
    container.innerHTML = ''; 

    if (nonResponsiveSites.length === 0) {
        container.innerHTML = '<div class="text-center text-muted-light py-3">No non-responsive sites recorded yet.</div>';
        const clearAllBtn = document.getElementById('removeAllNonResponsiveBtn');
        if (clearAllBtn) clearAllBtn.style.display = 'none';
    } else {
        nonResponsiveSites.forEach(site => {
            const item = document.createElement('div');
            item.className = 'non-responsive-site-item list-group-item list-group-item-warning d-flex justify-content-between align-items-center';
            item.innerHTML = `
                <span>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>${site.name}</strong> <small class="text-muted-light">(${site.url})</small>
                </span>
                <button class="btn btn-sm btn-outline-danger action-icon" title="Remove from this list" onclick="removeNonResponsiveSite('${site.name}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(item);
        });
        const clearAllBtn = document.getElementById('removeAllNonResponsiveBtn');
        if (clearAllBtn) {
            clearAllBtn.style.display = 'block';
        } else {
            
            const buttonContainer = document.getElementById('nonResponsiveActionsContainer'); 
            if (buttonContainer) {
                const newClearAllBtn = document.createElement('button');
                newClearAllBtn.id = 'removeAllNonResponsiveBtn';
                newClearAllBtn.className = 'btn btn-sm btn-danger mt-2';
                newClearAllBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Remove All Non-Responsive';
                newClearAllBtn.onclick = confirmRemoveAllNonResponsive;
                buttonContainer.appendChild(newClearAllBtn);
            }
        }
    }

    
    const badge = document.getElementById('non-responsive-tab-badge'); 
    if (badge) {
        badge.textContent = nonResponsiveSites.length;
        badge.style.display = nonResponsiveSites.length > 0 ? 'inline-block' : 'none';
    }
}

function addProcessedUrl(url, status, message = '') {
    const processedUrlsContainer = document.getElementById('processedUrlsContainer');
    if (!processedUrlsContainer) return;

    if (processedUrlsContainer.firstChild && processedUrlsContainer.firstChild.textContent && processedUrlsContainer.firstChild.textContent.includes('Processed URLs will appear here')) {
        processedUrlsContainer.innerHTML = ''; 
    }

    let statusIcon, statusClass, statusText;
    switch (status) {
        case 'skipped':
            statusIcon = 'fas fa-ban text-warning';
            statusClass = 'list-group-item-warning';
            statusText = 'Skipped';
            break;
        case 'failed':
            statusIcon = 'fas fa-times-circle text-danger';
            statusClass = 'list-group-item-danger';
            statusText = 'Failed';
            break;
        case 'success':
        default:
            statusIcon = 'fas fa-check-circle text-success';
            statusClass = 'list-group-item-success';
            statusText = 'Success';
            break;
    }

    const item = document.createElement('div');
    item.className = `processed-url-item list-group-item ${statusClass}`;
    item.innerHTML = `
        <div class="d-flex w-100 justify-content-between">
            <h5 class="mb-1"><i class="${statusIcon} me-2"></i> ${statusText}</h5>
            <small class="text-muted-light">${new Date().toLocaleTimeString()}</small>
        </div>
        <p class="mb-1">URL: <a href="${url}" target="_blank">${url}</a></p>
        ${message ? `<small class="text-muted-light"><em>${message}</em></small>` : ''}
    `;
    processedUrlsContainer.insertBefore(item, processedUrlsContainer.firstChild);

    
    const currentCount = parseInt(document.getElementById('processed-urls-tab-badge')?.textContent || '0') + 1;
    const badge = document.getElementById('processed-urls-tab-badge');
    if (badge) {
        badge.textContent = currentCount;
        badge.style.display = currentCount > 0 ? 'inline-block' : 'none';
    }
}

function loadPingSitesFromServer() {
    addLog('Loading ping sites from server...', 'info');
    fetch('ajax_handler.php?action=load_ping_sites')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.pingSites) {
                pingSites = data.pingSites;
                addLog('Ping sites loaded successfully.', 'success');
            } else {
                addLog('Failed to load ping sites from server, using defaults. ' + (data.message || ''), 'warning');
                
                if (!pingSites || pingSites.length === 0) {
                    pingSites = [
                        { name: "Google", url: "https://www.google.com/ping?sitemap=", enabled: true, status: 'unknown' },
                        { name: "Bing", url: "https://www.bing.com/ping?sitemap=", enabled: true, status: 'unknown'  },
                        { name: "Yandex", url: "https://webmaster.yandex.com/ping?sitemap=", enabled: true, status: 'unknown'  },
                        { name: "IndexNow", url: "https://api.indexnow.org/indexnow?url=[url]&key=[key]", enabled: true, status: 'unknown' } 
                    ];
                }
            }
            renderPingSitesList();
        })
        .catch(error => {
            addLog('Error loading ping sites: ' + error, 'error');
            
            if (!pingSites || pingSites.length === 0) {
                 pingSites = [
                    { name: "Google", url: "https://www.google.com/ping?sitemap=", enabled: true, status: 'unknown' },
                    { name: "Bing", url: "https://www.bing.com/ping?sitemap=", enabled: true, status: 'unknown' },
                    { name: "Yandex", url: "https://webmaster.yandex.com/ping?sitemap=", enabled: true, status: 'unknown' },
                    { name: "IndexNow", url: "https://api.indexnow.org/indexnow?url=[url]&key=[key]", enabled: true, status: 'unknown' }
                ];
            }
            renderPingSitesList();
        });
}

function renderPingSitesList() {
    const listContainer = document.getElementById('pingSitesListContainer');
    if (!listContainer) return;
    listContainer.innerHTML = ''; 

    if (pingSites.length === 0) {
        listContainer.innerHTML = '<p class="text-muted-light">No ping sites configured. Add some!</p>';
        return;
    }

    pingSites.forEach((site, index) => {
        const item = document.createElement('div');
        item.className = 'ping-site-item list-group-item d-flex justify-content-between align-items-center';
        
        let statusIndicator = '';
        if (site.status === 'active') {
            statusIndicator = '<span class="ping-site-status active" title="Recently responsive"></span>';
        } else if (site.status === 'inactive') {
            statusIndicator = '<span class="ping-site-status inactive" title="Recently non-responsive"></span>';
        } else {
            statusIndicator = '<span class="ping-site-status unknown" title="Status unknown"></span>';
        }

        item.innerHTML = `
            <div>
                ${statusIndicator}
                <strong class="ping-site-name">${site.name}</strong>
                <div class="ping-site-url text-muted-light"><small>${site.url}</small></div>
            </div>
            <div class="ping-site-actions">
                <div class="form-check form-switch me-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="pingSiteToggle-${index}" ${site.enabled ? 'checked' : ''} onchange="togglePingSite(${index})">
                    <label class="form-check-label visually-hidden" for="pingSiteToggle-${index}">Enable</label>
                </div>
                <button class="btn btn-sm btn-outline-primary action-icon edit" title="Edit" onclick="editPingSite(${index})"><i class="fas fa-edit"></i></button>
                <button class="btn btn-sm btn-outline-danger action-icon delete" title="Remove" onclick="confirmRemovePingSite(${index})"><i class="fas fa-trash-alt"></i></button>
            </div>
        `;
        listContainer.appendChild(item);
    });
}

function togglePingSite(index) {
    if (pingSites[index]) {
        pingSites[index].enabled = !pingSites[index].enabled;
        pingSitesModified = true;
        addLog(`Ping site '${pingSites[index].name}' ${pingSites[index].enabled ? 'enabled' : 'disabled'}.`, 'info');
        
    }
}

function confirmRemovePingSite(index) {
    const siteName = pingSites[index] ? pingSites[index].name : 'this site';
    document.getElementById('confirmRemoveMessage').textContent = `Are you sure you want to remove ${siteName}?`;
    
    document.getElementById('confirmRemoveButton').dataset.siteIndex = index; 
    window.confirmRemoveModal.show();
}

function executeRemovePingSite() {
    const index = parseInt(document.getElementById('confirmRemoveButton').dataset.siteIndex);
    if (index >= 0 && index < pingSites.length) {
        const removedSite = pingSites.splice(index, 1)[0];
        pingSitesModified = true;
        addLog(`Ping site '${removedSite.name}' removed.`, 'info');
        renderPingSitesList();
    }
    window.confirmRemoveModal.hide();
}

let editingPingSiteIndex = -1; 

function addNewPingSite() {
    editingPingSiteIndex = -1;
    document.getElementById('pingSiteModalTitle').textContent = 'Add New Ping Site';
    document.getElementById('pingSiteName').value = '';
    document.getElementById('pingSiteUrl').value = '';
    document.getElementById('pingSiteEnabled').checked = true;
    window.addEditPingSiteModal.show();
}

function editPingSite(index) {
    if (index >= 0 && index < pingSites.length) {
        editingPingSiteIndex = index;
        const site = pingSites[index];
        document.getElementById('pingSiteModalTitle').textContent = 'Edit Ping Site';
        document.getElementById('pingSiteName').value = site.name;
        document.getElementById('pingSiteUrl').value = site.url;
        document.getElementById('pingSiteEnabled').checked = site.enabled;
        window.addEditPingSiteModal.show();
    }
}

function savePingSite() {
    const name = document.getElementById('pingSiteName').value.trim();
    const url = document.getElementById('pingSiteUrl').value.trim();
    const enabled = document.getElementById('pingSiteEnabled').checked;

    if (!name || !url) {
        alert('Site Name and URL are required.');
        return;
    }
    
    try {
        new URL(url.replace('[sitemap]', 'https://example.com/sitemap.xml').replace('[url]', 'https://example.com').replace('[key]', 'testkey'));
    } catch (_) {
        alert('Please enter a valid URL structure. Use [sitemap], [url], or [key] as placeholders where appropriate.');
        return;
    }

    const siteData = { name, url, enabled, status: editingPingSiteIndex !== -1 ? pingSites[editingPingSiteIndex].status : 'unknown' };

    if (editingPingSiteIndex === -1) { 
        pingSites.push(siteData);
        addLog(`Ping site '${name}' added.`, 'info');
    } else { 
        pingSites[editingPingSiteIndex] = siteData;
        addLog(`Ping site '${name}' updated.`, 'info');
    }
    pingSitesModified = true;
    renderPingSitesList();
    window.addEditPingSiteModal.hide();
}

function savePingSitesToServer() {
    if (!pingSitesModified) {
        addLog('No changes to ping sites to save.', 'info');
        window.managePingSitesModal.hide();
        return;
    }
    addLog('Saving ping sites to server...', 'info');
    fetch('ajax_handler.php?action=save_ping_sites', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pingSites: pingSites }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('Ping sites saved successfully.', 'success');
            pingSitesModified = false; 
        } else {
            addLog('Failed to save ping sites: ' + (data.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        addLog('Error saving ping sites: ' + error, 'error');
    })
    .finally(() => {
        window.managePingSitesModal.hide();
    });
}

async function showSelectUrlsModal() {
    if (extractedUrls.length === 0) {
        addLog('No URLs loaded. Fetching now...', 'info');
        const success = await fetchUrls();
        if (!success) {
            addLog('Could not fetch URLs. Please check your settings and try again.', 'error');
            return; 
        }
        
        if (extractedUrls.length === 0) {
            return; 
        }
    }
    renderUrlSelectionList();
    window.selectUrlsModal.show();
}

function renderUrlSelectionList() {
    const listContainer = document.getElementById('urlSelectionListContainer');
    if (!listContainer) return;
    listContainer.innerHTML = '';

    if (extractedUrls.length === 0) {
        listContainer.innerHTML = '<p class="text-muted-light">No URLs available.</p>';
        return;
    }

    extractedUrls.forEach((urlObj, index) => {
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-center';
        item.innerHTML = `
            <div class="form-check">
                <input class="form-check-input url-select-checkbox" type="checkbox" value="${urlObj.url}" id="urlSelect-${index}" ${urlObj.selected ? 'checked' : ''} ${urlObj.skipped ? 'disabled' : ''}>
                <label class="form-check-label ${urlObj.skipped ? 'text-decoration-line-through text-muted-light' : ''}" for="urlSelect-${index}">
                    ${urlObj.url}
                </label>
            </div>
            ${urlObj.skipped ? '<span class="badge bg-secondary">Skipped</span>' : ''}
        `;
        listContainer.appendChild(item);
    });
}

function selectAllUrls(select = true) {
    extractedUrls.forEach(urlObj => {
        if (!urlObj.skipped) {
            urlObj.selected = select;
        }
    });
    renderUrlSelectionList();
}

function markSelectedUrlsToSkip(skip = true) {
    let count = 0;
    extractedUrls.forEach(urlObj => {
        const checkbox = document.querySelector(`.url-select-checkbox[value="${CSS.escape(urlObj.url)}"]`);
        if (checkbox && checkbox.checked && !urlObj.skipped) {
            urlObj.skipped = skip;
            urlObj.selected = false; 
            count++;
        }
    });
    addLog(`${count} URLs marked to be ${skip ? 'skipped' : 'included'}.`, 'info');
    renderUrlSelectionList();
    updateUrlsBasedOnSelection(); 
}

function applyUrlSelection() {
    
    extractedUrls.forEach(urlObj => {
        if (!urlObj.skipped) {
            const checkbox = document.querySelector(`.url-select-checkbox[value="${CSS.escape(urlObj.url)}"]`);
            if (checkbox) {
                urlObj.selected = checkbox.checked;
            }
        }
    });
    updateUrlsBasedOnSelection();
    window.selectUrlsModal.hide();
    addLog('URL selection applied.', 'info');
}

function updateUrlsBasedOnSelection() {
    urls = extractedUrls.filter(u => u.selected && !u.skipped).map(u => u.url);
    totalUrls = urls.length;
    document.getElementById('urlCount').textContent = totalUrls;
    updateProgress(stats.urlsProcessed, totalUrls);
    if (totalUrls > 0) {
        addLog(`${totalUrls} URLs are ready for processing.`, 'info');
    } else {
        addLog('No URLs selected for processing.', 'warning');
    }
}

function applyUrlLimit() {
    
    
    if (extractedUrls.length === 0) return; 

    
    
    extractedUrls.forEach(urlObj => {
        if (!urlObj.skipped) {
            const checkbox = document.querySelector(`.url-select-checkbox[value="${CSS.escape(urlObj.url)}"]`);
            if (checkbox) {
                urlObj.selected = checkbox.checked;
            }
        }
    });

    let potentialUrls = extractedUrls.filter(u => u.selected && !u.skipped);
    if (urlLimit > 0 && potentialUrls.length > urlLimit) {
        urls = potentialUrls.slice(0, urlLimit).map(u => u.url);
        addLog(`URL list limited to ${urlLimit} URLs. ${potentialUrls.length - urlLimit} URLs deferred.`, 'info');
    } else {
        urls = potentialUrls.map(u => u.url);
        if (urlLimit > 0) {
            addLog(`All ${urls.length} selected URLs are within the limit of ${urlLimit}.`, 'info');
        }
    }
    totalUrls = urls.length;
    document.getElementById('urlCount').textContent = totalUrls;
    updateProgress(stats.urlsProcessed, totalUrls);
    if (totalUrls === 0 && extractedUrls.length > 0) {
        addLog('No URLs available for processing after applying limits/selections.', 'warning');
    }
}

function fetchUrls() {
    const feedUrl = document.getElementById('feedUrl').value.trim();
    const sitemapUrl = document.getElementById('sitemapUrl').value.trim();
    const urlLimit = document.getElementById('urlLimit').value;
    backlinkParallelWorkers = parseInt(document.getElementById('parallelWorkers').value) || 1;

    if (!feedUrl && !sitemapUrl) {
        addLog('Please enter a Feed URL or Sitemap URL.', 'warning');
        return Promise.resolve(false); 
    }

    addLog('Fetching URLs...', 'info');
    const fetchUrlsButton = document.getElementById('fetchUrlsButton');
    if (fetchUrlsButton) {
        fetchUrlsButton.disabled = true;
        fetchUrlsButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Fetching...';
    }

    return fetch('ajax_handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'fetch_urls',
            feedUrl: feedUrl,
            sitemapUrl: sitemapUrl,
            maxUrlsToProcess: parseInt(urlLimit)
        }),
    })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.urls && data.urls.length > 0) {
                extractedUrls = data.urls.map(url => ({ url: url, selected: true, skipped: false })); 
                addLog(`${extractedUrls.length} URLs fetched successfully from ${data.totalCount} total URLs (${data.feedCount || 0} from feed, ${data.sitemapCount || 0} from sitemap). You can now manage selection or start processing.`, 'success');
                
                if (data.skippedCount > 0) {
                    addLog(`Skipped ${data.skippedCount} problematic URLs containing 'cesta.html'`, 'warning');
                }
                
                if (data.limitApplied) {
                    addLog(`Limited to ${data.maxUrlsToProcess} URLs as per configuration`, 'info');
                }
                
                applyUrlLimit(); 
                const manageUrlsButton = document.getElementById('manageUrlsButton');
                const selectUrlsButton = document.getElementById('selectUrlsButton');
                const startButton = document.getElementById('startButton');

                if (manageUrlsButton) manageUrlsButton.disabled = false;
                if (selectUrlsButton) selectUrlsButton.disabled = extractedUrls.length === 0;

                if (totalUrls > 0 && startButton) {
                    startButton.disabled = false;
                }
                return true; 
            } else {
                addLog('Failed to fetch URLs or no URLs found. ' + (data.error || data.message || ''), 'error');
                extractedUrls = [];
                urls = [];
                totalUrls = 0;
                document.getElementById('urlCount').textContent = 0;
                const manageUrlsButton = document.getElementById('manageUrlsButton');
                const selectUrlsButton = document.getElementById('selectUrlsButton');
                const startButton = document.getElementById('startButton');

                if (manageUrlsButton) manageUrlsButton.disabled = true;
                if (selectUrlsButton) selectUrlsButton.disabled = true;
                if (startButton) startButton.disabled = true;
                return false; 
            }
        })
        .catch(error => {
            addLog('Error fetching URLs: ' + error, 'error');
            extractedUrls = [];
            urls = [];
            totalUrls = 0;
            document.getElementById('urlCount').textContent = 0;
            const manageUrlsButton = document.getElementById('manageUrlsButton');
            const selectUrlsButton = document.getElementById('selectUrlsButton');
            const startButton = document.getElementById('startButton');

            if (manageUrlsButton) manageUrlsButton.disabled = true;
            if (selectUrlsButton) selectUrlsButton.disabled = true;
            if (startButton) startButton.disabled = true;
            return false; 
        })
        .finally(() => {
            const fetchUrlsButton = document.getElementById('fetchUrlsButton');
            if (fetchUrlsButton) {
                fetchUrlsButton.disabled = false;
                fetchUrlsButton.innerHTML = '<i class="fas fa-download"></i> Fetch URLs';
            }
        });
}

async function processUrl(urlToProcess) {
    if (shouldStop) {
        addLog(`Skipping ${urlToProcess} due to stop request.`, 'warning');
        stats.urlsSkipped++;
        addProcessedUrl(urlToProcess, 'skipped', 'Process stopped by user');
        return;
    }

    addLog(`Processing URL: ${urlToProcess}`, 'info');
    let pingsForThisUrl = 0;
    let successfulPingsForThisUrl = 0;

    
    const sitesToPing = pingSites.filter(site => site.enabled && site.name.toLowerCase() !== 'indexnow');
    if (sitesToPing.length === 0) {
        addLog(`No enabled ping sites (excluding IndexNow) for ${urlToProcess}. Skipping pinging.`, 'warning');
        
    } else {
        for (const site of sitesToPing) {
            if (shouldStop) break;
            addLog(`Pinging ${site.name} for ${urlToProcess}...`, 'info');
            try {
                const response = await fetch('ajax_handler.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=ping_site&ping_url=${encodeURIComponent(site.url)}&target_url=${encodeURIComponent(urlToProcess)}&site_name=${encodeURIComponent(site.name)}`
                });
                const result = await response.json();
                pingsForThisUrl++;
                if (result.success) {
                    stats.successPings++;
                    successfulPingsForThisUrl++;
                    addLog(`Successfully pinged ${site.name} for ${urlToProcess}. Response: ${result.message || 'OK'}`, 'success');
                    addResultItem(urlToProcess, site.name, 'success', result.message || 'OK');
                    
                    if (site.status !== 'active') {
                        site.status = 'active';
                        pingSitesModified = true; 
                        renderPingSitesList(); 
                    }
                } else {
                    stats.failedPings++;
                    addLog(`Failed to ping ${site.name} for ${urlToProcess}. Error: ${result.message || 'Unknown error'}`, 'error');
                    addResultItem(urlToProcess, site.name, 'failure', result.message || 'Unknown error');
                    addFailedLink(urlToProcess, site.name, result.message || 'Unknown error');
                    if (result.isNonResponsive) {
                        addNonResponsiveSite(site.name, site.url);
                        site.status = 'inactive'; 
                        pingSitesModified = true;
                        renderPingSitesList();
                    }
                }
            } catch (error) {
                stats.failedPings++;
                pingsForThisUrl++;
                addLog(`Network error pinging ${site.name} for ${urlToProcess}: ${error}`, 'error');
                addResultItem(urlToProcess, site.name, 'failure', `Network error: ${error.message}`);
                addFailedLink(urlToProcess, site.name, `Network error: ${error.message}`);
                
                addNonResponsiveSite(site.name, site.url); 
                site.status = 'inactive';
                pingSitesModified = true;
                renderPingSitesList();
            }
        }
    }

    
    const indexNowSite = pingSites.find(site => site.name.toLowerCase() === 'indexnow' && site.enabled);
    if (indexNowSite && !shouldStop) {
        await runIndexNow([urlToProcess]); 
    }

    stats.urlsProcessed++;
    if (pingsForThisUrl > 0) {
        addProcessedUrl(urlToProcess, successfulPingsForThisUrl > 0 ? 'success' : 'failed', `${successfulPingsForThisUrl}/${pingsForThisUrl} pings successful.`);
    } else if (indexNowSite && !shouldStop) {
        
        
    } else {
        addProcessedUrl(urlToProcess, 'skipped', 'No standard ping services enabled or attempted.');
    }
}

async function startProcess() {
    if (isProcessing) {
        addLog('Process already running.', 'warning');
        return;
    }
    if (urls.length === 0) {
        addLog('No URLs to process. Fetch or select URLs first.', 'warning');
        const fetchSuccess = await fetchUrls(); 
        if (!fetchSuccess || urls.length === 0) {
             addLog('URL fetching failed or no URLs available after fetch. Aborting process.', 'error');
            return;
        }
        addLog('URLs fetched. You can now select specific URLs or click Start again to process all.', 'info');
        
        const selectUrlsButton = document.getElementById('selectUrlsButton');
        if (selectUrlsButton && extractedUrls.length > 0) {
            selectUrlsButton.disabled = false;
        }
        return;
    }

    isProcessing = true;
    shouldStop = false;
    currentUrlIndex = 0;
    stats.urlsProcessed = 0;
    stats.urlsSkipped = 0;
    stats.successPings = 0;
    stats.failedPings = 0;
    nonResponsiveSites = []; 
    failedLinks = []; 

    
    document.getElementById('resultsContainer').innerHTML = '<div class="text-center text-muted-light py-3">Results will appear here as URLs are processed.</div>';
    document.getElementById('failedLinksContainer').innerHTML = '<div class="text-center text-muted-light py-3">Failed links will appear here.</div>';
    document.getElementById('nonResponsiveSitesContainer').innerHTML = '<div class="text-center text-muted-light py-3">Non-responsive ping sites will appear here.</div>';
    document.getElementById('processedUrlsContainer').innerHTML = '<div class="text-center text-muted-light py-3">Processed URLs will appear here.</div>';
    
    ['failed-tab-badge', 'non-responsive-tab-badge', 'processed-urls-tab-badge'].forEach(id => {
        const badge = document.getElementById(id);
        if (badge) {
            badge.textContent = '0';
            badge.style.display = 'none';
        }
    });
    renderNonResponsiveSitesList(); 

    updateStatsDisplay();
    updateProgress(0, totalUrls);

    const startButton = document.getElementById('startButton');
    const fetchUrlsButton = document.getElementById('fetchUrlsButton');
    const manageUrlsButton = document.getElementById('manageUrlsButton');
    const urlLimitSlider = document.getElementById('urlLimitSlider');
    
    if (startButton) startButton.disabled = true;
    if (fetchUrlsButton) fetchUrlsButton.disabled = true;
    if (manageUrlsButton) manageUrlsButton.disabled = true;
    if (urlLimitSlider) urlLimitSlider.disabled = true;
    document.getElementById('parallelWorkers').disabled = true;
    document.getElementById('stopButton').disabled = false;

    addLog(`Starting process for ${totalUrls} URLs with ${backlinkParallelWorkers} parallel worker(s).`, 'info');

    const batchSize = backlinkParallelWorkers;
    while (currentUrlIndex < totalUrls && !shouldStop) {
        const batch = urls.slice(currentUrlIndex, currentUrlIndex + batchSize);
        const promises = batch.map(url => processUrl(url).then(() => {
            
            updateStatsDisplay();
            updateProgress(stats.urlsProcessed + stats.urlsSkipped, totalUrls);
        }));
        await Promise.all(promises); 
        currentUrlIndex += batch.length; 
    }

    finishProcess();
}

function stopProcess() {
    if (isProcessing) {
        shouldStop = true;
        addLog('Stop request received. Finishing current operations...', 'warning');
        document.getElementById('stopButton').disabled = true;
        document.getElementById('stopButton').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Stopping...';
    }
}

function finishProcess() {
    isProcessing = false;
    const outcomeMessage = shouldStop ? 'Process stopped by user.' : 'All URLs processed.';
    addLog(outcomeMessage, shouldStop ? 'warning' : 'success');
    
    const startButton = document.getElementById('startButton');
    const fetchUrlsButton = document.getElementById('fetchUrlsButton');
    const manageUrlsButton = document.getElementById('manageUrlsButton');
    const selectUrlsButton = document.getElementById('selectUrlsButton');
    const urlLimitSlider = document.getElementById('urlLimitSlider');
    
    if (startButton) startButton.disabled = urls.length === 0; 
    if (fetchUrlsButton) fetchUrlsButton.disabled = false;
    if (manageUrlsButton) manageUrlsButton.disabled = extractedUrls.length === 0;
    if (selectUrlsButton) selectUrlsButton.disabled = extractedUrls.length === 0;
    if (urlLimitSlider) urlLimitSlider.disabled = false;
    document.getElementById('parallelWorkers').disabled = false;
    document.getElementById('stopButton').disabled = true;
    document.getElementById('stopButton').innerHTML = '<i class="fas fa-stop"></i> Stop';

    updateStatsDisplay(); 
    if (stats.urlsProcessed + stats.urlsSkipped === totalUrls || shouldStop) {
         updateProgress(stats.urlsProcessed + stats.urlsSkipped, totalUrls);
    }

    if (pingSitesModified) {
        addLog('Changes to ping sites were made during processing. Consider saving them via Manage Ping Sites.', 'info');
        
        
    }
    
    const indexNowLog = document.getElementById('indexNowLog');
    if (indexNowLog && indexNowLog.innerHTML !== '' && indexNowLog.innerHTML !== 'IndexNow results will appear here.') {
        addLog('IndexNow processing complete. Check IndexNow Log tab for details.', 'info');
    }
}

async function runIndexNow(urlsToIndex) {
    const indexNowSite = pingSites.find(site => site.name.toLowerCase() === 'indexnow' && site.enabled);
    if (!indexNowSite) {
        addLog('IndexNow service is not enabled or configured.', 'warning');
        return;
    }

    const apiKey = document.getElementById('indexNowApiKey').value.trim();
    const host = document.getElementById('indexNowHost').value.trim(); 

    if (!apiKey || !host) {
        addLog('IndexNow API Key and Host are required.', 'error');
        document.getElementById('indexNowStatus').innerHTML = '<span class="text-danger">API Key and Host required.</span>';
        return;
    }

    if (!urlsToIndex || urlsToIndex.length === 0) {
        addLog('No URLs provided for IndexNow submission.', 'warning');
        return;
    }

    addLog(`Starting IndexNow submission for ${urlsToIndex.length} URL(s)...`, 'info');
    document.getElementById('indexNowStatus').innerHTML = '<span class="spinner-border spinner-border-sm"></span> Submitting to IndexNow...';
    const indexNowLogContainer = document.getElementById('indexNowLog');
    if (indexNowLogContainer.firstChild && indexNowLogContainer.firstChild.textContent && indexNowLogContainer.firstChild.textContent.includes('IndexNow results will appear here')) {
        indexNowLogContainer.innerHTML = ''; 
    }

    try {
        const response = await fetch('ajax_handler.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `action=index_now&host=${encodeURIComponent(host)}&key=${encodeURIComponent(apiKey)}&urlList=${encodeURIComponent(JSON.stringify(urlsToIndex))}`
        });
        
        const resultText = await response.text(); 
        let resultJson;
        try {
            resultJson = JSON.parse(resultText);
        } catch (e) {
            addLog(`IndexNow: Unexpected response format. Server said: ${resultText}`, 'error');
            document.getElementById('indexNowStatus').innerHTML = `<span class="text-danger">Error: Unexpected response.</span>`;
            indexNowLogContainer.innerHTML += `<div class="log-entry log-error">[${new Date().toLocaleTimeString()}] IndexNow submission failed. Unexpected response: ${resultText}</div>`;
            urlsToIndex.forEach(url => {
                addProcessedUrl(url, 'failed', 'IndexNow submission error - unexpected response');
                stats.failedPings++; 
            });
            return;
        }

        if (response.ok && resultJson.success) { 
            addLog(`IndexNow submission successful for ${urlsToIndex.length} URL(s). Response: ${resultJson.message || 'OK'}`, 'success');
            document.getElementById('indexNowStatus').innerHTML = `<span class="text-success">Successfully submitted ${urlsToIndex.length} URL(s).</span>`;
            indexNowLogContainer.innerHTML += `<div class="log-entry log-success">[${new Date().toLocaleTimeString()}] ${resultJson.message || `Successfully submitted ${urlsToIndex.length} URL(s).`}</div>`;
            urlsToIndex.forEach(url => {
                addProcessedUrl(url, 'success', 'Submitted to IndexNow');
                stats.successPings++; 
            });
             if (indexNowSite.status !== 'active') {
                indexNowSite.status = 'active';
                pingSitesModified = true;
                renderPingSitesList();
            }
        } else {
            
            let errorDetail = resultJson.message || `HTTP ${response.status}`; 
            if (resultJson.errors) { 
                errorDetail += ": " + resultJson.errors.join(', ');
            }
            addLog(`IndexNow submission failed for ${urlsToIndex.length} URL(s). Status: ${response.status}. Error: ${errorDetail}`, 'error');
            document.getElementById('indexNowStatus').innerHTML = `<span class="text-danger">Error: ${errorDetail}</span>`;
            indexNowLogContainer.innerHTML += `<div class="log-entry log-error">[${new Date().toLocaleTimeString()}] IndexNow submission failed. ${errorDetail}</div>`;
            urlsToIndex.forEach(url => {
                addProcessedUrl(url, 'failed', `IndexNow: ${errorDetail}`);
                stats.failedPings++;
            });
            if (response.status === 403 || response.status === 422 || response.status === 429) { 
                 if (indexNowSite.status !== 'inactive') {
                    indexNowSite.status = 'inactive';
                    pingSitesModified = true;
                    renderPingSitesList();
                }
            }
        }
    } catch (error) {
        addLog(`IndexNow submission network error: ${error}`, 'error');
        document.getElementById('indexNowStatus').innerHTML = `<span class="text-danger">Network Error.</span>`;
        indexNowLogContainer.innerHTML += `<div class="log-entry log-error">[${new Date().toLocaleTimeString()}] IndexNow submission network error: ${error.message}</div>`;
        urlsToIndex.forEach(url => {
            addProcessedUrl(url, 'failed', `IndexNow Network Error: ${error.message}`);
            stats.failedPings++;
        });
        if (indexNowSite.status !== 'inactive') {
            indexNowSite.status = 'inactive';
            pingSitesModified = true;
            renderPingSitesList();
        }
    }
    
    updateStatsDisplay(); 
}

document.addEventListener('DOMContentLoaded', () => {
    const submitIndexNowButton = document.getElementById('submitIndexNowButton');
    if (submitIndexNowButton) {
        submitIndexNowButton.addEventListener('click', () => {
            const urlsText = document.getElementById('indexNowUrls').value.trim();
            if (!urlsText) {
                addLog('No URLs entered for IndexNow submission.', 'warning');
                document.getElementById('indexNowStatus').innerHTML = '<span class="text-warning">Please enter URLs.</span>';
                return;
            }
            const urlsToIndex = urlsText.split(/[\n\s,]+/).map(u => u.trim()).filter(u => u);
            if (urlsToIndex.length > 0) {
                runIndexNow(urlsToIndex);
            } else {
                addLog('No valid URLs found in the input for IndexNow.', 'warning');
                document.getElementById('indexNowStatus').innerHTML = '<span class="text-warning">No valid URLs.</span>';
            }
        });
    }
});