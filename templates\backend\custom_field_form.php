<?php

$is_editing = isset($custom_field) && !empty($custom_field);
$form_title = $is_editing ? "Editar Campo Personalizado" : "Criar Novo Campo Personalizado";
$form_action = "admin.php";

$field_id = $is_editing ? $custom_field['id'] : '';
$field_name = $is_editing ? $custom_field['name'] : '';
$field_description = $is_editing ? $custom_field['description'] : '';
$field_type_id = $is_editing ? $custom_field['field_type_id'] : '';
$field_min_chars = $is_editing ? $custom_field['min_chars'] : 0;
$field_max_chars = $is_editing ? $custom_field['max_chars'] : 255;
$field_price_modifier = $is_editing ? $custom_field['price_modifier'] : 0;
$field_is_required = $is_editing ? $custom_field['is_required'] : 0;
$field_is_active = $is_editing ? $custom_field['is_active'] : 1;
$field_config = $is_editing && !empty($custom_field['config_json']) ? json_decode($custom_field['config_json'], true) : [];

$field_type_slug = $is_editing ? $custom_field['type_slug'] : '';

if (isset($form_data)) {
    $field_name = $form_data['name'] ?? $field_name;
    $field_description = $form_data['description'] ?? $field_description;
    $field_type_id = $form_data['field_type_id'] ?? $field_type_id;
    $field_min_chars = $form_data['min_chars'] ?? $field_min_chars;
    $field_max_chars = $form_data['max_chars'] ?? $field_max_chars;
    $field_price_modifier = $form_data['price_modifier'] ?? $field_price_modifier;
    $field_is_required = $form_data['is_required'] ?? $field_is_required;
    $field_is_active = $form_data['is_active'] ?? $field_is_active;
    $field_config = $form_data['config'] ?? $field_config;
}
?>

<h1><?= $form_title ?></h1>

<div class="card">
    <div class="card-body">
        <form id="customFieldForm" method="post" action="<?= $form_action ?>" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="<?= generate_csrf_token() ?>">
            <input type="hidden" name="section" value="custom_fields">
            <input type="hidden" name="action" value="<?= $is_editing ? 'update' : 'create' ?>">
            <?php if ($is_editing): ?>
                <input type="hidden" name="id" value="<?= $field_id ?>">
            <?php endif; ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="name" class="form-label">Nome *</label>
                    <input type="text" class="form-control" id="name" name="name" value="<?= sanitize_input($field_name) ?>" required>
                </div>
                <div class="col-md-6">
                    <label for="field_type_id" class="form-label">Tipo de Campo *</label>
                    <select class="form-select" id="field_type_id" name="field_type_id" required>
                        <option value="">Selecione um tipo</option>
                        <?php foreach ($field_types as $type): ?>
                            <option value="<?= $type['id'] ?>" data-slug="<?= $type['slug'] ?>" <?= $field_type_id == $type['id'] ? 'selected' : '' ?>>
                                <?= sanitize_input($type['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Descrição</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?= sanitize_input($field_description) ?></textarea>
                <div class="form-text">Descrição opcional para ajudar os compradores a entender o propósito deste campo.</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="price_modifier" class="form-label">Modificador de Preço (<?= get_setting('currency_symbol', '€') ?>)</label>
                    <input type="number" step="0.01" class="form-control" id="price_modifier" name="price_modifier" value="<?= sanitize_input($field_price_modifier) ?>">
                    <div class="form-text">Valor adicional a cobrar quando este campo é utilizado.</div>
                </div>
                <div class="col-md-4">
                    <label for="is_required" class="form-label">Campo Obrigatório</label>
                    <select class="form-select" id="is_required" name="is_required">
                        <option value="0" <?= $field_is_required == 0 ? 'selected' : '' ?>>Não</option>
                        <option value="1" <?= $field_is_required == 1 ? 'selected' : '' ?>>Sim</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="is_active" class="form-label">Estado</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="1" <?= $field_is_active == 1 ? 'selected' : '' ?>>Ativo</option>
                        <option value="0" <?= $field_is_active == 0 ? 'selected' : '' ?>>Inativo</option>
                    </select>
                </div>
            </div>

            <!-- Text Field Specific Options -->
            <div id="textFieldOptions" class="field-type-options" style="display: none;">
                <h4 class="mt-4 mb-3">Opções para Campo de Texto</h4>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="min_chars" class="form-label">Mínimo de Caracteres</label>
                        <input type="number" class="form-control" id="min_chars" name="min_chars" value="<?= sanitize_input($field_min_chars) ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="max_chars" class="form-label">Máximo de Caracteres</label>
                        <input type="number" class="form-control" id="max_chars" name="max_chars" value="<?= sanitize_input($field_max_chars) ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Fontes Disponíveis</label>
                    <div class="row">
                        <?php foreach ($fonts as $font): ?>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="config[allowed_fonts][]" value="<?= $font['id'] ?>" id="font_<?= $font['id'] ?>"
                                        <?= isset($field_config['allowed_fonts']) && in_array($font['id'], $field_config['allowed_fonts']) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="font_<?= $font['id'] ?>">
                                        <?= sanitize_input($font['name']) ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="form-text">Selecione as fontes que estarão disponíveis para este campo de texto.</div>
                </div>
            </div>

            <!-- File Upload Field Specific Options -->
            <div id="fileUploadOptions" class="field-type-options" style="display: none;">
                <h4 class="mt-4 mb-3">Opções para Campo de Upload de Ficheiro</h4>
                <div class="mb-3">
                    <label class="form-label">Tipos de Ficheiros Permitidos</label>
                    <div class="row">
                        <?php
                        $allowed_file_types = [
                            
                            'eps' => 'EPS (Encapsulated PostScript)',
                            'svg' => 'SVG (Scalable Vector Graphics)',
                            'cdr' => 'CDR (CorelDRAW)',
                            'ps' => 'PS (PostScript)',
                            'afdesign' => 'Affinity Designer',

                            
                            'jpg' => 'JPG/JPEG (Imagem)',
                            'jpeg' => 'JPEG (Imagem)',
                            'png' => 'PNG (Imagem)',
                            'bmp' => 'BMP (Imagem)',

                            
                            'zip' => 'ZIP (Arquivo Comprimido)',
                            'rar' => 'RAR (Arquivo Comprimido)'
                        ];

                        $selected_file_types = isset($field_config['allowed_file_types']) ? $field_config['allowed_file_types'] : array_keys($allowed_file_types);
                        ?>

                        <?php foreach ($allowed_file_types as $ext => $label): ?>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="config[allowed_file_types][]" value="<?= $ext ?>" id="file_type_<?= $ext ?>"
                                        <?= in_array($ext, $selected_file_types) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="file_type_<?= $ext ?>">
                                        <?= sanitize_input($label) ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="form-text">Selecione os tipos de ficheiros que serão aceites para este campo.</div>
                </div>

                <div class="mb-3">
                    <label for="custom_extensions" class="form-label">Extensões Personalizadas</label>
                    <input type="text" class="form-control" id="custom_extensions" name="config[custom_extensions]"
                           value="<?= isset($field_config['custom_extensions']) ? $field_config['custom_extensions'] : '' ?>"
                           placeholder="Exemplo: ai, psd, pdf (separadas por vírgula)">
                    <div class="form-text">Adicione extensões personalizadas separadas por vírgula (sem ponto).</div>
                </div>

                <div class="mb-3">
                    <label for="max_file_size" class="form-label">Tamanho Máximo do Ficheiro (MB)</label>
                    <input type="number" step="0.1" min="0.1" max="10" class="form-control" id="max_file_size" name="config[max_file_size]"
                           value="<?= isset($field_config['max_file_size']) ? $field_config['max_file_size'] : 10 ?>">
                    <div class="form-text">Tamanho máximo permitido para o ficheiro (até 10 MB).</div>
                </div>
            </div>

            <!-- Dropdown Field Specific Options -->
            <div id="dropdownFieldOptions" class="field-type-options" style="display: none;">
                <h4 class="mt-4 mb-3">Opções para Campo Dropdown</h4>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Opções do Dropdown</label>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="addDropdownOption">
                            <i class="fas fa-plus"></i> Adicionar Opção
                        </button>
                    </div>
                    
                    <div id="dropdownOptionsContainer">
                        <?php 
                        $dropdown_options = [];
                        if ($is_editing && $custom_field) {
                            $dropdown_options = get_custom_field_dropdown_options($custom_field['id'], false);
                        }
                        
                        if (empty($dropdown_options)): 
                        ?>
                        <div class="dropdown-option-row border rounded p-3 mb-2" data-index="0">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Texto da Opção</label>
                                    <input type="text" class="form-control" name="dropdown_options[0][option_text]" placeholder="Ex: Pequeno">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Valor</label>
                                    <input type="text" class="form-control" name="dropdown_options[0][option_value]" placeholder="Ex: small">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Modificador de Preço (€)</label>
                                    <input type="number" step="0.01" class="form-control" name="dropdown_options[0][price_modifier]" value="0.00">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Ordem</label>
                                    <input type="number" class="form-control" name="dropdown_options[0][sort_order]" value="0">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">Ativo</label>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="dropdown_options[0][is_active]" value="1" checked>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-sm btn-outline-danger d-block remove-option" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <?php foreach ($dropdown_options as $index => $option): ?>
                        <div class="dropdown-option-row border rounded p-3 mb-2" data-index="<?= $index ?>">
                            <input type="hidden" name="dropdown_options[<?= $index ?>][id]" value="<?= $option['id'] ?>">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Texto da Opção</label>
                                    <input type="text" class="form-control" name="dropdown_options[<?= $index ?>][option_text]" value="<?= sanitize_input($option['option_text']) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Valor</label>
                                    <input type="text" class="form-control" name="dropdown_options[<?= $index ?>][option_value]" value="<?= sanitize_input($option['option_value']) ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Modificador de Preço (€)</label>
                                    <input type="number" step="0.01" class="form-control" name="dropdown_options[<?= $index ?>][price_modifier]" value="<?= $option['price_modifier'] ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Ordem</label>
                                    <input type="number" class="form-control" name="dropdown_options[<?= $index ?>][sort_order]" value="<?= $option['sort_order'] ?>">
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">Ativo</label>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="dropdown_options[<?= $index ?>][is_active]" value="1" <?= $option['is_active'] ? 'checked' : '' ?>>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-sm btn-outline-danger d-block remove-option">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-text">Configure as opções que estarão disponíveis no dropdown. O modificador de preço será adicionado ao preço base do produto.</div>
                </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Guardar</button>
                <a href="admin.php?section=custom_fields&<?= get_session_id_param() ?>" class="btn btn-secondary ms-2">Cancelar</a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fieldTypeSelect = document.getElementById('field_type_id');
    const textFieldOptions = document.getElementById('textFieldOptions');
    const fileUploadOptions = document.getElementById('fileUploadOptions');
    const dropdownFieldOptions = document.getElementById('dropdownFieldOptions');
    const addDropdownOptionBtn = document.getElementById('addDropdownOption');
    const dropdownOptionsContainer = document.getElementById('dropdownOptionsContainer');
    
    let optionIndex = <?= count($dropdown_options) > 0 ? count($dropdown_options) : 1 ?>;

    // Function to show/hide field type specific options
    function toggleFieldTypeOptions() {
        const selectedOption = fieldTypeSelect.options[fieldTypeSelect.selectedIndex];
        const fieldTypeSlug = selectedOption.getAttribute('data-slug');

        // Hide all field type specific options
        document.querySelectorAll('.field-type-options').forEach(el => {
            el.style.display = 'none';
        });

        // Show options based on field type
        if (fieldTypeSlug === 'custom-text') {
            textFieldOptions.style.display = 'block';
        } else if (fieldTypeSlug === 'file-upload') {
            fileUploadOptions.style.display = 'block';
        } else if (fieldTypeSlug === 'texto-dropdown') {
            dropdownFieldOptions.style.display = 'block';
        }
    }

    // Function to add new dropdown option
    function addDropdownOption() {
        const optionHtml = `
            <div class="dropdown-option-row border rounded p-3 mb-2" data-index="${optionIndex}">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Texto da Opção</label>
                        <input type="text" class="form-control" name="dropdown_options[${optionIndex}][option_text]" placeholder="Ex: Pequeno">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Valor</label>
                        <input type="text" class="form-control" name="dropdown_options[${optionIndex}][option_value]" placeholder="Ex: small">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Modificador de Preço (€)</label>
                        <input type="number" step="0.01" class="form-control" name="dropdown_options[${optionIndex}][price_modifier]" value="0.00">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Ordem</label>
                        <input type="number" class="form-control" name="dropdown_options[${optionIndex}][sort_order]" value="${optionIndex}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">Ativo</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="dropdown_options[${optionIndex}][is_active]" value="1" checked>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-sm btn-outline-danger d-block remove-option">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        dropdownOptionsContainer.insertAdjacentHTML('beforeend', optionHtml);
        optionIndex++;
        updateRemoveButtons();
    }

    // Function to remove dropdown option
    function removeDropdownOption(button) {
        const optionRow = button.closest('.dropdown-option-row');
        optionRow.remove();
        updateRemoveButtons();
    }

    // Function to update remove button states
    function updateRemoveButtons() {
        const optionRows = dropdownOptionsContainer.querySelectorAll('.dropdown-option-row');
        const removeButtons = dropdownOptionsContainer.querySelectorAll('.remove-option');
        
        removeButtons.forEach(button => {
            button.disabled = optionRows.length <= 1;
        });
    }

    // Event listeners
    addDropdownOptionBtn.addEventListener('click', addDropdownOption);
    
    dropdownOptionsContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-option')) {
            removeDropdownOption(e.target.closest('.remove-option'));
        }
    });

    // Initial toggle
    toggleFieldTypeOptions();
    updateRemoveButtons();

    // Toggle on change
    fieldTypeSelect.addEventListener('change', toggleFieldTypeOptions);
});
</script>
