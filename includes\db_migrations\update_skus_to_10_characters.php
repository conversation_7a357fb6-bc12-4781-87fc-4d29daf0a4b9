<?php

function migrate_update_skus_to_10_characters(PDO $pdo): void
{
    try {
        $pdo->beginTransaction();

        
        $products_with_short_skus = $pdo->query("
            SELECT id, name_pt, sku
            FROM products
            WHERE sku IS NOT NULL AND LENGTH(sku) < 10
        ")->fetchAll();

        foreach ($products_with_short_skus as $product) {
            $current_sku = $product['sku'];
            $product_name = $product['name_pt'];

            
            $new_sku = generate_10_char_sku_from_existing($current_sku, $product_name);

            
            $counter = 0;
            $original_new_sku = $new_sku;
            while (sku_exists_in_migration($pdo, $new_sku, $product['id']) && $counter < 100) {
                $counter++;
                $new_sku = substr($original_new_sku, 0, 9) . $counter;
            }

            
            $stmt = $pdo->prepare("UPDATE products SET sku = ? WHERE id = ?");
            $stmt->execute([$new_sku, $product['id']]);
        }

        
        $variations_with_short_skus = $pdo->query("
            SELECT pv.id, pv.sku, p.name_pt
            FROM product_variations pv
            JOIN products p ON pv.product_id = p.id
            WHERE pv.sku IS NOT NULL AND LENGTH(pv.sku) < 10
        ")->fetchAll();

        foreach ($variations_with_short_skus as $variation) {
            $current_sku = $variation['sku'];
            $product_name = $variation['name_pt'];

            
            $new_sku = generate_10_char_sku_from_existing($current_sku, $product_name);

            
            $counter = 0;
            $original_new_sku = $new_sku;
            while (sku_exists_in_migration($pdo, $new_sku, null, $variation['id']) && $counter < 100) {
                $counter++;
                $new_sku = substr($original_new_sku, 0, 9) . $counter;
            }

            
            $stmt = $pdo->prepare("UPDATE product_variations SET sku = ? WHERE id = ?");
            $stmt->execute([$new_sku, $variation['id']]);
        }

        $pdo->commit();

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function generate_10_char_sku_from_existing(string $current_sku, string $product_name): string
{
    
    if (strlen($current_sku) >= 8) {
        $sku = strtoupper($current_sku);
        while (strlen($sku) < 10) {
            $sku .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
        }
        return substr($sku, 0, 10);
    }

    
    $words = explode(' ', $product_name);
    $initials = '';
    foreach ($words as $word) {
        if (!empty($word)) {
            $initials .= strtoupper(mb_substr($word, 0, 1));
        }
    }

    
    $base = strtoupper($current_sku);
    if (strlen($base) < 6) {
        $base .= substr($initials, 0, 6 - strlen($base));
    }

    
    while (strlen($base) < 10) {
        $base .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
    }

    return substr($base, 0, 10);
}

function sku_exists_in_migration(PDO $pdo, string $sku, ?int $exclude_product_id = null, ?int $exclude_variation_id = null): bool
{
    
    $sql = "SELECT id FROM products WHERE sku = ?";
    $params = [$sku];

    if ($exclude_product_id) {
        $sql .= " AND id != ?";
        $params[] = $exclude_product_id;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    if ($stmt->fetch()) {
        return true;
    }

    
    $sql = "SELECT id FROM product_variations WHERE sku = ?";
    $params = [$sku];

    if ($exclude_variation_id) {
        $sql .= " AND id != ?";
        $params[] = $exclude_variation_id;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return (bool) $stmt->fetch();
}

