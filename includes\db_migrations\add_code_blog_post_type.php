<?php

function migrate_add_code_blog_post_type(PDO $pdo) {
    try {
        $pdo->beginTransaction();

        
        $columns = $pdo->query("PRAGMA table_info(blog_posts);")->fetchAll(PDO::FETCH_COLUMN, 1);
        if (!in_array('code_content', $columns)) {
            $pdo->exec("ALTER TABLE blog_posts ADD COLUMN code_content TEXT;");
            echo "Column 'code_content' added to 'blog_posts'.\n";
        } else {
            echo "Column 'code_content' already exists in 'blog_posts'.\n";
        }

        
        
        $pdo->exec("
            CREATE TABLE blog_posts_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                slug TEXT UNIQUE NOT NULL,
                post_type TEXT NOT NULL CHECK(post_type IN ('article', 'link', 'CODE')),
                content TEXT,
                link_url TEXT,
                link_description TEXT,
                image_path TEXT NOT NULL,
                is_published INTEGER NOT NULL DEFAULT 0,
                published_at TEXT,
                seo_title TEXT,
                seo_description TEXT,
                seo_keywords TEXT,
                og_title TEXT,
                og_description TEXT,
                og_image TEXT,
                twitter_card TEXT,
                twitter_title TEXT, -- Corrected from twitter_titleTEXT
                twitter_description TEXT,
                twitter_image TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                image_description TEXT,
                code_content TEXT
            );
        ");
        echo "Table 'blog_posts_new' created with updated CHECK constraint.\n";

        
        
        
        $pdo->exec("
            INSERT INTO blog_posts_new (
                id, title, slug, post_type, content, link_url, link_description,
                image_path, is_published, published_at, seo_title, seo_description,
                seo_keywords, og_title, og_description, og_image, twitter_card,
                twitter_title, twitter_description, twitter_image, created_at,
                updated_at, image_description, code_content
            )
            SELECT
                id, title, slug, post_type, content, link_url, link_description,
                image_path, is_published, published_at, seo_title, seo_description,
                seo_keywords, og_title, og_description, og_image, twitter_card,
                twitter_title, -- Corrected from twitter_titleTEXT
                twitter_description, twitter_image, created_at,
                updated_at, image_description, code_content
            FROM blog_posts;
        ");
        echo "Data copied from 'blog_posts' to 'blog_posts_new'.\n";

        
        $pdo->exec("DROP TABLE blog_posts;");
        echo "Old table 'blog_posts' dropped.\n";

        
        $pdo->exec("ALTER TABLE blog_posts_new RENAME TO blog_posts;");
        echo "Table 'blog_posts_new' renamed to 'blog_posts'.\n";

        
        $pdo->exec("CREATE INDEX idx_blog_posts_slug ON blog_posts (slug);");
        $pdo->exec("CREATE INDEX idx_blog_posts_published ON blog_posts (is_published, published_at);");
        $pdo->exec("CREATE INDEX idx_blog_posts_type ON blog_posts (post_type);");
        echo "Indexes recreated for 'blog_posts'.\n";

        $pdo->commit();
        echo "Migration 'add_code_blog_post_type' completed successfully.\n";
        return true;

    } catch (PDOException $e) {
        $pdo->rollBack();
        echo "Migration 'add_code_blog_post_type' failed: " . $e->getMessage() . "\n";
        return false;
    }
}

function has_code_blog_post_type_migrated(PDO $pdo) {
    try {
        $stmt = $pdo->query("PRAGMA table_info(blog_posts);");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $has_code_content = false;
        $post_type_check_is_updated = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'code_content') {
                $has_code_content = true;
            }
        }

        
        
        $stmt = $pdo->query("SELECT sql FROM sqlite_master WHERE type='table' AND name='blog_posts';");
        $table_sql = $stmt->fetchColumn();
        if ($table_sql && strpos($table_sql, "CHECK(post_type IN ('article', 'link', 'CODE'))") !== false) {
            $post_type_check_is_updated = true;
        }

        return $has_code_content && $post_type_check_is_updated;
    } catch (PDOException $e) {
        return false; 
    }
}

?>