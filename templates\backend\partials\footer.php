<!-- End of main content area (opened in header.php) -->

<!-- No visible footer needed if using sidebar layout, but keep for structure -->
<!--
<footer class="footer mt-auto py-3 bg-body-tertiary text-center">
  <div class="container">
    <span class="text-body-secondary">Admin Area - &copy; <?= date('Y') ?></span>
  </div>
</footer>
-->

<!-- Bootstrap Bundle with <PERSON><PERSON> -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

<!-- Summernote JS -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.js"></script>

<!-- Summernote Bootstrap 5 Fix -->
<script src="<?= get_asset_url('js/summernote-bs5-fix.js') ?>"></script>

<!-- Admin URL Helper -->
<script src="<?= get_asset_url('js/admin-url-helper.js') ?>"></script>

<!-- Admin Utilities JS -->
<script src="<?= get_asset_url('js/admin-utils.js') ?>"></script>

<!-- Summernote Configuration -->
<script src="<?= get_asset_url('js/summernote-config.js') ?>"></script>

<!-- Bootstrap Dropdown Fix -->
<script src="<?= get_asset_url('js/bootstrap-dropdown-fix.js') ?>"></script>

<!-- Admin Navigation JS -->
<script src="<?= get_asset_url('js/admin-navigation.js') ?>"></script>

<!-- Custom Admin JS -->
<script src="<?= get_asset_url('js/admin.js') ?>"></script>

<!-- VAT Rates Management JS (Specific to Settings page, but load globally for simplicity or add conditional loading) -->
<script src="<?= get_asset_url('js/admin-vat-rates.js') ?>"></script>

<!-- Admin Modal Handler JS -->
<script src="<?= get_asset_url('js/admin-modal-handler.js') ?>"></script>

<!-- Admin Sitemap JS -->
<script src="<?= get_asset_url('js/admin-sitemap.js') ?>"></script>

<!-- Admin Placeholder Links JS -->
<script src="<?= get_asset_url('js/admin-placeholder-links.js') ?>"></script>

<!-- Chart.js (for dashboard) -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

<!-- Admin Dashboard JS (only load on dashboard page) -->
<?php if (isset($section) && $section === 'dashboard'): ?>
<link href="<?= get_asset_url('css/admin-dashboard.css') ?>" rel="stylesheet">
<script>
  // Ensure Chart.js is fully loaded before initializing dashboard
  function loadDashboardScript() {
    
    if (typeof Chart === 'undefined') {
      // Chart.js not loaded yet, wait and try again
      setTimeout(loadDashboardScript, 150);
      return;
    }
    
    // Check if dashboard script already exists to avoid duplicates
    const existingScript = document.querySelector('script[src*="admin-dashboard.js"]');
    if (existingScript) {
      if (typeof window.initDashboard === 'function') {
        window.initDashboard();
      } else {
        setTimeout(() => {
          if (typeof window.initDashboard === 'function') {
            window.initDashboard();
          } else {
          }
        }, 200);
      }
      return;
    }

    // Chart.js is loaded, now load dashboard script
    const script = document.createElement('script');
    script.src = '<?= get_asset_url('js/admin-dashboard.js') ?>';
    script.onload = function() {
      // Retry mechanism for initDashboard function availability
      let attempts = 0;
      const maxAttempts = 5;
      const interval = 100;
      function attemptInit() {
        if (typeof window.initDashboard === 'function') {
          window.initDashboard();
        } else if (attempts < maxAttempts) {
          attempts++;
          console.warn(`initDashboard not ready (footer initial load), attempt ${attempts}/${maxAttempts}. Retrying in ${interval}ms...`);
          setTimeout(attemptInit, interval);
        } else {
        }
      }
      attemptInit();
    };
    script.onerror = function() {
    };
    document.body.appendChild(script);
  }

  // Start the loading process
  setTimeout(loadDashboardScript, 50); // Reduced initial delay slightly
</script>
<?php endif; ?>

<!-- Initialize Components -->
<script>
  $(document).ready(function() {
    // Mobile Sidebar Toggle
    $('.sidebar-toggle').on('click', function() {
      $('.admin-sidebar').toggleClass('show');
    });

    // Close sidebar when clicking outside on mobile
    $(document).on('click', function(e) {
      if ($(window).width() < 768) {
        if (!$(e.target).closest('.admin-sidebar').length &&
            !$(e.target).closest('.sidebar-toggle').length) {
          $('.admin-sidebar').removeClass('show');
        }
      }
    });

    // We don't need this anymore since we're handling initialization in the dashboard-specific script
    // The dashboard will be initialized by the script loader above

    // Trigger a custom event to notify that content is loaded
    document.dispatchEvent(new CustomEvent('contentLoaded'));
  });
</script>

<?php
if (isset($_SESSION['prompt_send_license_email']) && $_SESSION['prompt_send_license_email']) {
    $license_id_to_prompt = (int)$_SESSION['prompt_send_license_email'];
    
    
    
    
    
    $csrf_token_for_js = isset($admin_view_data['csrf_token_for_js']) ? $admin_view_data['csrf_token_for_js'] : (isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : '');

?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const licenseId = <?= $license_id_to_prompt ?>;
        const csrfToken = '<?= htmlspecialchars($csrf_token_for_js, ENT_QUOTES, 'UTF-8') ?>';
        const sessionIdParam = '<?= htmlspecialchars(get_session_id_param(), ENT_QUOTES, 'UTF-8') ?>';
        const adminBaseUrl = 'admin.php'; // Make sure this is correct

        if (confirm('Deseja enviar o link de download direto para o cliente?')) {
            let redirectUrl = `${adminBaseUrl}?section=licenses&action=send_direct_download_link&id=${licenseId}&${sessionIdParam}`;
            if (csrfToken) {
                redirectUrl += `&csrf_token=${encodeURIComponent(csrfToken)}`;
            }
            window.location.href = redirectUrl;
        }
    });
</script>
<?php
    unset($_SESSION['prompt_send_license_email']);
}
?>
</body>
</html>
