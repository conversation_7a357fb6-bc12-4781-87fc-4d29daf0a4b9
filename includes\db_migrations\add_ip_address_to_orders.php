<?php

function add_ip_address_to_orders_migration($pdo) {
    try {
        
        $stmt = $pdo->query("PRAGMA table_info(orders)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $ip_address_exists = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'ip_address') {
                $ip_address_exists = true;
                break;
            }
        }
        
        if (!$ip_address_exists) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN ip_address TEXT");
        } else {
            
        }
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}