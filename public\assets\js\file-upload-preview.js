

document.addEventListener('DOMContentLoaded', function() {

    
    initFontPreviews();

    
    const uploadAreas = document.querySelectorAll('.custom-field-upload-label');

    uploadAreas.forEach(container => {
        const input = container.querySelector('.custom-field-file');
        if (!input) return;

        
        if (input.required) {
            const placeholder = container.querySelector('.upload-placeholder');
            if (placeholder) {
                const requiredIndicator = document.createElement('div');
                requiredIndicator.className = 'text-xs text-red-500 mt-1 text-center';
                requiredIndicator.textContent = '* Campo obrigatório';
                placeholder.appendChild(requiredIndicator);
            }
        }

        
        input.addEventListener('change', handleFileSelect);

        
        const placeholder = container.querySelector('.upload-placeholder');
        if (placeholder) {
            placeholder.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                input.click();
            });
        }

        
        const preview = container.querySelector('.file-preview');
        if (preview) {
            
            const thumbnail = preview.querySelector('.file-thumbnail');
            const fileIcon = preview.querySelector('.file-icon');

            if (thumbnail) {
                thumbnail.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    input.click();
                });
            }

            if (fileIcon) {
                fileIcon.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    input.click();
                });
            }
        }

        
        const removeButton = container.querySelector('.remove-file');
        if (removeButton) {
            removeButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                resetFileInput(input);
            });
        }
    });

    

    function handleFileSelect(e) {
        const input = e.target;
        const file = input.files[0];

        if (!file) {
            resetFileInput(input);
            return;
        }

        
        const container = input.closest('.custom-field-upload-label');
        if (!container) return;

        
        container.classList.remove('border-red-500');

        const placeholder = container.querySelector('.upload-placeholder');
        const preview = container.querySelector('.file-preview');
        const fileName = container.querySelector('.file-name');
        const fileSize = container.querySelector('.file-size');
        const thumbnail = container.querySelector('.file-thumbnail');
        const fileIcon = container.querySelector('.file-icon');

        
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);

        
        placeholder.style.display = 'none';
        preview.style.display = 'flex';

        
        const isImage = /\.(jpg|jpeg|png|gif|bmp|svg)$/i.test(file.name);

        if (isImage) {
            
            const reader = new FileReader();
            reader.onload = function(e) {
                thumbnail.innerHTML = `<img src="${e.target.result}" class="max-h-16 max-w-full rounded" alt="Preview">`;
                thumbnail.style.display = 'block';
                fileIcon.style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            
            thumbnail.style.display = 'none';
            fileIcon.style.display = 'block';

            
            const fileExtension = file.name.split('.').pop().toLowerCase();
            let iconClass = 'ri-file-line';

            
            const iconMap = {
                'pdf': 'ri-file-pdf-line',
                'doc': 'ri-file-word-line',
                'docx': 'ri-file-word-line',
                'xls': 'ri-file-excel-line',
                'xlsx': 'ri-file-excel-line',
                'ppt': 'ri-file-ppt-line',
                'pptx': 'ri-file-ppt-line',
                'zip': 'ri-file-zip-line',
                'rar': 'ri-file-zip-line',
                'txt': 'ri-file-text-line',
                'eps': 'ri-image-line',
                'cdr': 'ri-image-line',
                'ai': 'ri-image-line',
                'psd': 'ri-image-line',
                'afdesign': 'ri-image-line'
            };

            if (iconMap[fileExtension]) {
                iconClass = iconMap[fileExtension];
            }

            fileIcon.innerHTML = `<i class="${iconClass} text-4xl text-primary"></i>`;
        }
    }

    

    function resetFileInput(input) {
        
        input.value = '';

        
        const container = input.closest('.custom-field-upload-label');
        if (!container) return;

        const placeholder = container.querySelector('.upload-placeholder');
        const preview = container.querySelector('.file-preview');

        
        placeholder.style.display = 'flex';
        preview.style.display = 'none';

        
        const thumbnail = container.querySelector('.file-thumbnail');
        if (thumbnail) thumbnail.innerHTML = '';
    }

    

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    

    const uploadLabels = document.querySelectorAll('.custom-field-upload-label');

    uploadLabels.forEach(label => {
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            label.addEventListener(eventName, preventDefaults, false);
        });

        
        ['dragenter', 'dragover'].forEach(eventName => {
            label.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            label.addEventListener(eventName, unhighlight, false);
        });

        
        label.addEventListener('drop', handleDrop, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        this.classList.add('border-primary');
    }

    function unhighlight(e) {
        this.classList.remove('border-primary');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        const input = this.querySelector('.custom-field-file');

        if (input && files.length > 0) {
            input.files = files;
            
            const event = new Event('change', { bubbles: true });
            input.dispatchEvent(event);
        }
    }
});

function initFontPreviews() {

    
    const fontSelects = document.querySelectorAll('.custom-field-font');

    fontSelects.forEach(select => {
        const fieldId = select.getAttribute('data-field-id');
        if (!fieldId) return;

        const previewContainer = document.getElementById(`font-preview-${fieldId}`);
        const previewText = previewContainer ? previewContainer.querySelector('.font-preview-text') : null;
        const textField = document.querySelector(`textarea[data-field-id="${fieldId}"]`);

        if (!previewContainer || !previewText || !textField) {
            console.warn(`Missing preview elements for field ${fieldId}`);
            return;
        }

        
        select.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const fontFamily = selectedOption.getAttribute('data-font-family');

            if (fontFamily) {
                previewContainer.classList.remove('hidden');
                previewText.style.fontFamily = `'${fontFamily}', sans-serif`;

                
                updatePreviewText(textField, previewText);
            } else {
                previewContainer.classList.add('hidden');
            }
        });

        
        textField.addEventListener('input', function() {
            if (!select.value) return; 

            
            previewContainer.classList.remove('hidden');
            updatePreviewText(this, previewText);
        });

        
        function updatePreviewText(input, preview) {
            const text = input.value.trim();
            const displayText = text || 'Texto de exemplo';
            preview.textContent = displayText;
            
            
            adjustFontSize(preview, displayText);
        }
        
        
        function adjustFontSize(element, text) {
            
            element.style.fontSize = '';
            element.classList.remove('text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl', 'text-6xl');
            
            
            const textLength = text.length;
            let sizeClass = 'text-base'; 
            
            if (textLength <= 10) {
                sizeClass = 'text-5xl';
            } else if (textLength <= 15) {
                sizeClass = 'text-4xl';
            } else if (textLength <= 25) {
                sizeClass = 'text-3xl';
            } else if (textLength <= 40) {
                sizeClass = 'text-2xl';
            } else if (textLength <= 60) {
                sizeClass = 'text-xl';
            } else if (textLength <= 80) {
                sizeClass = 'text-lg';
            } else {
                sizeClass = 'text-base';
            }
            
            element.classList.add(sizeClass);
            
            
            const checkAndAdjust = () => {
                
                element.offsetHeight;
                
                let iterations = 0;
                const maxIterations = 8;
                
                while (element.scrollHeight > element.clientHeight && iterations < maxIterations) {
                    const currentSizes = ['text-6xl', 'text-5xl', 'text-4xl', 'text-3xl', 'text-2xl', 'text-xl', 'text-lg', 'text-base', 'text-sm', 'text-xs'];
                    const currentIndex = currentSizes.findIndex(size => element.classList.contains(size));
                    
                    if (currentIndex < currentSizes.length - 1) {
                        element.classList.remove(currentSizes[currentIndex]);
                        element.classList.add(currentSizes[currentIndex + 1]);
                        element.offsetHeight; 
                        iterations++;
                    } else {
                        break;
                    }
                }
                
                
                if (element.scrollHeight > element.clientHeight) {
                    element.classList.remove('text-xs');
                    element.style.fontSize = '0.6rem';
                }
            };
            
            
            requestAnimationFrame(checkAndAdjust);
        }
    });
}

function getCustomFieldData() {
    const customFieldData = [];

    
    document.querySelectorAll('.custom-field-text').forEach(field => {
        const fieldId = field.name.replace('custom_field_text_', '');
        const fontSelect = document.querySelector(`select[name="custom_field_font_${fieldId}"]`);

        
        if (field.value.trim() !== '' || field.required) {
            customFieldData.push({
                field_id: fieldId,
                type: 'custom-text',
                text_value: field.value.trim(), 
                font_id: fontSelect ? fontSelect.value : null
            });
        }
    });

    
    document.querySelectorAll('.custom-field-dropdown').forEach(field => {
        const fieldId = field.getAttribute('data-field-id');
        const selectedOption = field.options[field.selectedIndex];
        
        
        if (field.value !== '' || field.required) {
            customFieldData.push({
                field_id: fieldId,
                type: 'texto-dropdown',
                dropdown_value: field.value,
                dropdown_text: selectedOption ? selectedOption.getAttribute('data-option-text') || selectedOption.text : '',
                price_modifier: selectedOption ? parseFloat(selectedOption.getAttribute('data-price-modifier')) || 0 : 0
            });
        }
    });

    
    document.querySelectorAll('.custom-field-file').forEach(field => {
        const fieldId = field.getAttribute('data-field-id');

        
        if (field.files && field.files.length > 0) {
            customFieldData.push({
                field_id: fieldId,
                type: 'file-upload',
                file: field.files[0]
            });
        } else if (field.required) {
            
            
            customFieldData.push({
                field_id: fieldId,
                type: 'file-upload',
                file: null,
                is_required: true
            });
        }
    });
    return customFieldData;
}

document.addEventListener('DOMContentLoaded', function() {
    
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

    
    let isProcessingCartAddition = false;

    
    const handleAddToCartClick = async function(e) {
        
        e.stopPropagation();
        e.preventDefault();

        
        if (isProcessingCartAddition) {
            return;
        }

        isProcessingCartAddition = true;

        const productId = this.getAttribute('data-product-id');
        const variationId = this.getAttribute('data-variation-id');

        
        const isSimpleProduct = !this.hasAttribute('data-variation-id');

        
        let quantity = 1;
        const quantityInput = document.getElementById('detail-quantity');
        if (quantityInput) {
            quantity = parseInt(quantityInput.value) || 1;
        }
        const isValid = validateRequiredCustomFields();

        if (!isValid) {
            isProcessingCartAddition = false;

            
            if (typeof showToast === 'function') {
                showToast('Erro', 'Por favor, preencha todos os campos obrigatórios antes de adicionar ao carrinho.', 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Por favor, preencha todos os campos obrigatórios antes de adicionar ao carrinho.');
            }

            return;
        }

        
        const customFieldData = getCustomFieldData();

        try {
            
            const result = await addToCart(productId, variationId, quantity, customFieldData);

            
            if (!result.success && result.error && result.error.includes('obrigatório')) {

                
                const fieldNameMatch = result.error.match(/'([^']+)'/);
                if (fieldNameMatch && fieldNameMatch[1]) {
                    const fieldName = fieldNameMatch[1];

                    // Find the field in the DOM
                document.querySelectorAll('.custom-field-item').forEach(item => {
                    const label = item.querySelector('label');
                    if (label && label.textContent.includes(fieldName)) {

                        // Highlight the field
                        const textField = item.querySelector('textarea');
                        if (textField) {
                            textField.classList.add('border-red-500');
                            textField.focus();
                        }

                        const dropdownField = item.querySelector('select');
                        if (dropdownField) {
                            dropdownField.classList.add('border-red-500');
                            dropdownField.focus();
                        }

                        const fileField = item.querySelector('input[type="file"]');
                        if (fileField) {
                            const uploadLabel = fileField.closest('.custom-field-upload-label');
                            if (uploadLabel) {
                                uploadLabel.classList.add('border-red-500');
                                uploadLabel.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    }
                });
                }
            }
        } catch (error) {
        } finally {
            isProcessingCartAddition = false;
        }
    };

    // Add the event listener with capture=true to ensure it runs before the one in main.js
    addToCartButtons.forEach(button => {
        button.addEventListener('click', handleAddToCartClick, true);
    });

    /**
     * Validates all required custom fields
     * @returns {boolean} - True if all required fields are valid, false otherwise
     */
    function validateRequiredCustomFields() {
        let isValid = true;

        // Check if we have custom fields data in the window object
        if (!window.customFields || !Array.isArray(window.customFields)) {
            return true;
        }

        // Get all required fields
        const requiredFields = window.customFields.filter(field => field.is_required);

        if (requiredFields.length === 0) {
            return true;
        }

        // Check if the DOM elements for the required fields exist
        const missingFields = requiredFields.filter(field => {
            if (field.type === 'custom-text') {
                return !document.querySelector(`textarea[name="custom_field_text_${field.id}"]`);
            } else if (field.type === 'file-upload') {
                return !document.querySelector(`input[name="custom_field_file_${field.id}"]`);
            } else if (field.type === 'texto-dropdown') {
                return !document.querySelector(`select[name="custom_field_dropdown_${field.id}"]`);
            }
            return false;
        });

        if (missingFields.length > 0) {
            console.warn('Some required fields are missing from the DOM:', missingFields);
            // If fields are missing from the DOM, we can't validate them properly
            
            return true;
        }

        
        requiredFields.forEach(field => {
            if (field.type === 'custom-text') {
                const textField = document.querySelector(`textarea[name="custom_field_text_${field.id}"]`);

                if (textField) {
                    
                    const fieldValue = textField.value || '';
                    const trimmedValue = fieldValue.trim();
                    const isEmpty = trimmedValue === '';
                    const minLength = parseInt(textField.getAttribute('minlength')) || 0;
                    const maxLength = parseInt(textField.getAttribute('maxlength')) || 255;
                    const valueLength = trimmedValue.length;

                    if (isEmpty) {
                        isValid = false;

                        
                        const fieldName = field.name || `Campo #${field.id}`;
                        if (typeof showToast === 'function') {
                            showToast('Erro', `Campo de texto "${fieldName}" obrigatório não preenchido.`, 'ri-error-warning-line', 'border-red-500');
                        } else {
                            alert(`Campo de texto "${fieldName}" obrigatório não preenchido.`);
                        }

                        
                        textField.classList.add('border-red-500');
                        textField.focus();
                    } else if (minLength > 0 && valueLength < minLength) {
                        isValid = false;

                        
                        const fieldName = field.name || `Campo #${field.id}`;
                        if (typeof showToast === 'function') {
                            showToast('Erro', `Campo de texto "${fieldName}" deve ter no mínimo ${minLength} caracteres.`, 'ri-error-warning-line', 'border-red-500');
                        } else {
                            alert(`Campo de texto "${fieldName}" deve ter no mínimo ${minLength} caracteres.`);
                        }

                        
                        textField.classList.add('border-red-500');
                        textField.focus();
                    } else if (maxLength > 0 && valueLength > maxLength) {
                        isValid = false;

                        
                        const fieldName = field.name || `Campo #${field.id}`;
                        if (typeof showToast === 'function') {
                            showToast('Erro', `Campo de texto "${fieldName}" deve ter no máximo ${maxLength} caracteres.`, 'ri-error-warning-line', 'border-red-500');
                        } else {
                            alert(`Campo de texto "${fieldName}" deve ter no máximo ${maxLength} caracteres.`);
                        }

                        
                        textField.classList.add('border-red-500');
                        textField.focus();
                    } else {
                        textField.classList.remove('border-red-500');
                    }
                } else {
                    console.warn(`Required text field ${field.id} not found in the DOM`);
                }

                
                const fontSelect = document.querySelector(`select[name="custom_field_font_${field.id}"]`);
                if (fontSelect && fontSelect.required && (!fontSelect.value || fontSelect.value === '')) {
                    isValid = false;

                    
                    if (typeof showToast === 'function') {
                        showToast('Erro', 'Selecione uma fonte para o texto personalizado.', 'ri-error-warning-line', 'border-red-500');
                    } else {
                        alert('Selecione uma fonte para o texto personalizado.');
                    }

                    
                    fontSelect.classList.add('border-red-500');
                    fontSelect.focus();
                } else if (fontSelect) {
                    fontSelect.classList.remove('border-red-500');
                }
            } else if (field.type === 'texto-dropdown') {
                const dropdownField = document.querySelector(`select[name="custom_field_dropdown_${field.id}"]`);

                if (dropdownField && (!dropdownField.value || dropdownField.value === '')) {
                    isValid = false;

                    
                    const fieldName = field.name || `Campo #${field.id}`;
                    if (typeof showToast === 'function') {
                        showToast('Erro', `Campo dropdown "${fieldName}" obrigatório não selecionado.`, 'ri-error-warning-line', 'border-red-500');
                    } else {
                        alert(`Campo dropdown "${fieldName}" obrigatório não selecionado.`);
                    }

                    
                    dropdownField.classList.add('border-red-500');
                    dropdownField.focus();
                } else if (dropdownField) {
                    dropdownField.classList.remove('border-red-500');
                }
            } else if (field.type === 'file-upload') {
                const fileField = document.querySelector(`input[name="custom_field_file_${field.id}"]`);

                if (fileField && (!fileField.files || fileField.files.length === 0)) {
                    isValid = false;

                    
                    const fieldName = field.name || `Campo #${field.id}`;
                    if (typeof showToast === 'function') {
                        showToast('Erro', `Ficheiro obrigatório "${fieldName}" não carregado.`, 'ri-error-warning-line', 'border-red-500');
                    } else {
                        alert(`Ficheiro obrigatório "${fieldName}" não carregado.`);
                    }

                    
                    const uploadLabel = fileField.closest('.custom-field-upload-label');
                    if (uploadLabel) {
                        uploadLabel.classList.add('border-red-500');
                        uploadLabel.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                } else if (fileField) {
                    const uploadLabel = fileField.closest('.custom-field-upload-label');
                    if (uploadLabel) {
                        uploadLabel.classList.remove('border-red-500');
                    }

                    if (fileField.files && fileField.files.length > 0) {
                    }
                }
            }
        });

        return isValid;
    }
});
