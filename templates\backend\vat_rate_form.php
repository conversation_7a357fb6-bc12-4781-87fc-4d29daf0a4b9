<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}

$is_edit_mode = isset($vat_rate) && is_array($vat_rate);
$form_title = $is_edit_mode ? 'Editar Taxa de IVA' : 'Nova Taxa de IVA';
$form_action = $is_edit_mode ? 'update_vat' : 'create_vat';
$vat_id = $is_edit_mode ? $vat_rate['id'] : '';
$vat_rate_value = $is_edit_mode ? $vat_rate['rate'] : '';
$vat_description = $is_edit_mode ? $vat_rate['description'] : '';
$is_default = $is_edit_mode ? $vat_rate['is_default'] : 0;
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $form_title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="admin.php?<?= get_session_id_param() ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="admin.php?section=vat_rates&<?= get_session_id_param() ?>">Taxas de IVA</a></li>
        <li class="breadcrumb-item active"><?= $form_title ?></li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-percentage me-1"></i> <?= $form_title ?>
        </div>
        <div class="card-body">
            <form id="vat-rate-form" method="post" action="admin.php?ajax=1&<?= get_session_id_param() ?>">
                <input type="hidden" name="is_ajax" value="1">
                <input type="hidden" name="action" value="<?= $form_action ?>">
                <input type="hidden" name="csrf_token" value="<?= get_csrf_token() ?>">
                <?php if ($is_edit_mode): ?>
                    <input type="hidden" name="id" value="<?= $vat_id ?>">
                <?php endif; ?>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="rate" class="form-label">Taxa (%)</label>
                            <input type="number" class="form-control" id="rate" name="rate" 
                                   value="<?= htmlspecialchars($vat_rate_value) ?>" step="0.1" min="0" max="100" required>
                            <div class="form-text">Introduza a taxa de IVA em percentagem (ex: 23.0)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="description" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="description" name="description" 
                                   value="<?= htmlspecialchars($vat_description) ?>" required>
                            <div class="form-text">Descrição da taxa de IVA (ex: Taxa Normal, Taxa Reduzida)</div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1" 
                                   <?= $is_default ? 'checked' : '' ?> <?= $is_default ? 'disabled' : '' ?>>
                            <label class="form-check-label" for="is_default">
                                Definir como taxa predefinida
                            </label>
                            <?php if ($is_default): ?>
                                <input type="hidden" name="is_default" value="1">
                                <div class="form-text text-success">Esta é a taxa predefinida atual.</div>
                            <?php else: ?>
                                <div class="form-text">Se selecionado, esta taxa será usada como predefinida para novos produtos.</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Guardar
                    </button>
                    <a href="admin.php?section=vat_rates&<?= get_session_id_param() ?>" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-1"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('vat-rate-form');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const params = new URLSearchParams();
        
        for (const [key, value] of formData.entries()) {
            params.append(key, value);
        }
        
        fetch('admin.php?ajax=1&<?= get_session_id_param() ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to VAT rates list
                loadContent('vat_rates', 'list');
            } else {
                alert(data.message || 'Ocorreu um erro ao guardar a taxa de IVA.');
            }
        })
        .catch(error => {
            alert('Ocorreu um erro ao processar o pedido.');
        });
    });
});
</script>
