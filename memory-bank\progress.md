# Memory Bank File: progress.md

## Current System Status
The platform has undergone significant development, particularly in digital product management, license handling, order processing, and admin interface usability. A coupon system has been fully implemented. Many features align with specific user preferences for simplicity and security. **Recently completed: Optimized IP-to-country lookup system by migrating from CSV to SQLite database.** This performance enhancement replaced CSV file parsing with SQLite database queries in the `get_country_from_ip()` function, improving lookup speed and scalability with 136,595 IP range records and proper indexing. **Previously completed: Fixed banner management blank page issue and cleaned up orphaned banner images.** This critical fix resolved blocking UI issues during banner updates/deletions by removing synchronous cleanup calls and implementing a standalone cleanup script that successfully reduced orphaned images from 12 to 4 files. **Previously completed: Fixed session data deserialization issue that was preventing cart contents from displaying on the admin sessions page.** This fix resolved PHP `unserialize()` warnings in error logs and implemented robust error handling for various session data formats. Earlier work included IP tracking and country flag display for admin sessions management, sitemaps navigation fixes, SKU standardization, free digital product labeling, token and session management enhancements, and a "CODE" blog post type. Focus is now on testing recently completed enhancements and planning further refinements.

## What Works (Key Completed Features & Enhancements)

**Admin & Maintenance:**
*   **IP-to-Country Lookup System (Latest):** Optimized geolocation functionality by migrating from CSV file parsing to SQLite database queries. Replaced `asn-country-ipv4.csv` processing in `get_country_from_ip()` function with `IPs.sqlite` database containing 136,595 IP range records. Implemented proper indexing with composite `idx_ip_range` index on `Starting_IP` and `Ending_IP` columns for fast lookups. Enhanced performance and scalability while maintaining accurate country code detection for session management and admin interface integration.
*   **Banner Management:** Complete banner CRUD operations with image upload, editing, and optimized cleanup system implemented for comprehensive banner management functionality.
*   **IP Tracking and Country Flag Display:** Implemented comprehensive IP address tracking for user sessions with geographic visualization in admin interface. Added `user_ip` column to `sessions` table via database migration, updated session management functions to capture IP addresses using `get_customer_ip()`, and enhanced admin sessions list to display country flags with hover tooltips. Integrated Flag Icons CSS library and `country.is` API for real-time geolocation with caching and error handling. Provides administrators with immediate visual identification of session origins for security monitoring.
*   **Sitemaps Navigation Non-AJAX Fix:** Fixed sitemaps sidebar navigation issue where accessing `/admin.php?section=sitemaps` via direct URL refresh would show dashboard instead of sitemaps page. Added missing 'sitemaps' case to main switch statement in `admin.php` (line ~5900+) that handles non-AJAX GET requests. This was the inverse of the banners AJAX issue - where banners was missing from AJAX handler, sitemaps was missing from the main handler. Database verification confirmed `sitemap_configs` table exists with proper schema.
*   **Banners Navigation AJAX Fix:** Fixed banners sidebar navigation issue where clicking the banners link would show dashboard until page refresh. Added missing 'banners' case to AJAX switch statement in `admin.php` (line ~1273) to match the existing non-AJAX case. This follows the same pattern as the previously solved digital_products issue (#19).
*   **Token Management (Maintenance Section - `admin.php?section=maintenance`):**
    *   Added "Limpar Tokens de Download ATIVOS (Não Expirados)": Deletes all non-expired tokens from `download_tokens`. Implemented via `cleanup_all_active_download_tokens()` in [`includes/maintenance_functions.php`](includes/maintenance_functions.php:38).
    *   Added "Limpar Tokens de Acesso ATIVOS (Não Expirados)": Deletes all non-expired tokens from `order_access_tokens`. Implemented via `cleanup_all_active_order_access_tokens()` in [`includes/maintenance_functions.php`](includes/maintenance_functions.php:63).
    *   UI buttons added to [`templates/backend/maintenance_simple.php`](templates/backend/maintenance_simple.php:1).
    *   POST actions handled in [`admin.php`](admin.php:1) ([`admin.php:3775`](admin.php:3775), [`admin.php:3789`](admin.php:3789)).
*   **Session Management (Admin Section - `admin.php?section=sessions`):**
    *   New "Gerir Sessões" admin page.
    *   Lists all active user sessions from `sessions` table with details: Session ID, Created At, Last Access, Expires At, and Cart Contents Summary.
    *   Allows deletion of individual sessions, which also deletes associated `download_tokens` (via `session_id`). `order_access_tokens` are not deleted as they lack a direct `session_id` link.
    *   Implemented `get_all_sessions_data()` ([`includes/session.php:435`](includes/session.php:435)) and `delete_session_and_associated_tokens()` ([`includes/session.php:470`](includes/session.php:470)) in [`includes/session.php`](includes/session.php:1).
    *   View template: [`templates/backend/sessions_list.php`](templates/backend/sessions_list.php:1).
    *   [`admin.php`](admin.php:1) updated for data prep ([`admin.php:565`](admin.php:565)), GET/AJAX template loading ([`admin.php:5692`](admin.php:5692), [`admin.php:1181`](admin.php:1181)), POST delete handling ([`admin.php:4152`](admin.php:4152)).
    *   Navigation link added to [`templates/backend/partials/header.php`](templates/backend/partials/header.php:371).
    *   Corrected issues with `session_decode()` and AJAX page loading for this section.

**Core E-commerce & Product Management:**
*   **SKU Standardization (Latest):** All product SKUs are now standardized to exactly 10 characters across all product types (regular, variation, digital). Updated SKU generation functions, validation logic, admin forms, and successfully migrated 17 existing products from 5-character to 10-character SKUs via database migration.
*   **Free Digital Product Labeling (Latest):** Digital products with `base_price = 0` now display a "GRÁTIS" label in the bottom-left corner of product thumbnails. These products maintain normal cart and checkout functionality without special handling.
*   **Simple Product & Custom Field Cart Logic:**
    *   Fixed JavaScript on product detail page ([`templates/frontend/product_detail.php`](templates/frontend/product_detail.php:1)) to correctly display stock status and enable/disable add-to-cart for simple products (physical & digital) when custom fields are interacted with.
    *   Fixed server-side validation in `add_to_cart` action ([`includes/ajax_handler.php`](includes/ajax_handler.php:287)) to prevent "Por favor, selecione as opções do produto." error for simple/digital products with custom fields. These products no longer require variation selection.
*   **Coupon System:** Fully implemented (Admin CRUD, percentage/fixed discounts, usage limits, min order value, validity period, frontend cart/checkout integration with AJAX application & auto-refresh, order integration, session storage).
*   **Stock Management (Non-Digital):** Stock reduction on order placement, restoration on cancellation/refund, admin "Restore Stock" button, prevention of double restoration. Tracked in `order_items`.
*   **Minimum Order Value & Free Shipping Threshold:** Configurable in admin, integrated into cart/checkout UI and calculations.
*   **Product Videos:** Support for uploaded .webm files or external URLs (e.g., YouTube) with thumbnail functionality.
*   **Custom Fields:** Duplication button, italicized descriptions, validation. Automatic deletion of attachments with order deletion. Resolved various bugs (modal, duplicates, detachment). Custom fields are now correctly processed during add-to-cart for all product types.
*   **Product SEO & Social Media Metatags:** Output on detail pages, similar to blog posts, with pre-fill option.
*   **Automatic Deletion of Product Images/Attachments:** When products/orders are deleted.
*   **Admin Product Form: Creation Date Save Fix:** The `created_at` date for products (all types) is now correctly saved when edited in the admin product form ([`admin.php`](admin.php:1)). Help text in [`templates/backend/product_form.php`](templates/backend/product_form.php:142) was also corrected for date format.

**Digital Products & License Management:**
*   **Digital File Upload & Editing Functionality Restored:**
    *   Successfully resolved the "Erro ao criar registro do arquivo digital" (Error creating digital file record) by adding the missing `display_name` column to the `digital_files` table via a database migration.
    *   Successfully resolved the "Erro ao guardar o produto digital: Falha ao remover associações de tipos de ficheiro existentes" (Error saving digital product: Failed to remove existing file type associations) when editing digital products. This was fixed by recreating the `digital_product_file_type_associations` table to correct foreign key issues and then ensuring this recreation migration does not run on every page load.
    *   File type associations for digital products are now correctly saved and persist after editing.
*   **Digital File Display Names & Enhanced Management:**
    *   Implemented a `display_name` column in `digital_files` table for user-friendly file identification in admin.
    *   Admins can set/edit this `display_name` when uploading files (via product form or dedicated file management) or directly in the file management list.
    *   File listings in admin ([`change_digital_file.php`](templates/backend/change_digital_file.php), [`digital_product_form.php`](templates/backend/digital_product_form.php), [`digital_files_management.php`](templates/backend/digital_files_management.php)) now prioritize showing `display_name`.
    *   Database schema refactored: `file_path` removed from `digital_products` (now solely in `digital_files`), and `digital_products` uses `digital_file_id` to link to `digital_files`.
    *   Orphaned digital files (not linked to any product) can be deleted from the `digital_files_management.php` interface.
*   **Full Digital Product Lifecycle:** Creation, metadata (automatic stock, hidden stock, limit 1 per cart, no options requirement), secure file storage, admin editing ([`digital_product_form.php`](templates/backend/digital_product_form.php)).
*   **License Generation & Management:** Format `xxxx-xxxx-JCS-xxxx-xxxx`. Admin tools to reset download counts, edit expiration dates, delete download history, activate/deactivate licenses (list & detail views).
*   **Secure Download Process:** Three-step verification (license code, email, security token). Secure file delivery ([`download_file.php`](templates/frontend/download_file.php)), download tracking, IP restrictions, session security, token validation.
*   **Order Status Integration:** Automatic license activation/deactivation based on order status changes (`processing`, `shipped`/`completed`, `cancelled`, `refunded`), with download token removal and email notifications.
*   **Frontend Display:** Digital product indicators, specific info on product/cart/checkout/order success pages. Shipping optional for digital-only orders.
*   **Admin Interface:** Licenses section, integration with order details, download statistics reporting.
*   **Security:** User email/name encryption in license section. Security codes invalidated after failed attempts, user notification, captcha on forms.
*   **Bug Fixes:** Corrected license email verification (decryption before comparison). Improved license vs. download validity display clarity.

**UI/UX & Admin Enhancements:**
*   **Admin List View Filters & Pagination:** Implemented for Pages and Blog Posts sections (title, category, status, etc.), with visual indicators and clear filter buttons.
*   **Floating Buttons Layout Fix (Frontend):** Resolved overlap between the floating product filter toggle button and the scroll-to-top button. Achieved by adjusting Tailwind CSS positioning classes in [`templates/frontend/partials/footer.php`](templates/frontend/partials/footer.php) and [`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php) for proper vertical/horizontal alignment and spacing.
*   **Filter Bar State Persistence (Frontend):** The collapsible product filter bar ([`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php)) now remembers its expanded/collapsed state across page loads using `localStorage`.
*   **Messages Section (Backoffice):** Redesigned UI, fixed "Remover" button (simple form submission with JS confirmation, no modal/AJAX).
*   **Maintenance Section:** Refactored for reliability (direct form submissions, flash messages, JS confirmations). Fixed DB backup and flash message HTML rendering. Includes tools for token cleanup, DB optimization, integrity check, backup. **Enhanced with new token and session management tools.**
*   **Dashboard Improvements:** Single-column layout, darker widgets, high-contrast text. Fixed JS issues with AJAX navigation using `initDashboardButtons` and MutationObserver.
*   **Order Success Page (Context-Aware):** Different messages for initial vs. revisits (cookieless DB tracking via `order_visits`). Data censoring on revisits, email verification to reveal data, option to resend order details.
*   **Cookieless Visit Control:** `is_first_visit` flag in `order_visits` correctly updated.
*   **Order-Specific Token Generation:** "Gerar novo token" in admin order detail works correctly.
*   **Download Statistics Decryption:** Fixed download statistics page to properly decrypt and display customer data that was previously shown in encrypted form. Implemented consistent decryption across all areas of the page including Top 5 Customers section, customer email dropdown, and detailed download history.
*   **Email Template Saving:** Fixed with validation rules in [`settings_handler.php`](includes/settings_handler.php).
*   **`cleanup_old_order_visits`:** Fully implemented.
*   **Footer Placeholder Links:** Implemented with admin management and alphabetical sorting.
*   **Blog Post Detail Page:** "Return Back" button, clickable category list.
*   **Blog Post Publishing:** Fixed issue with auto-filled OG/Twitter image URLs.
*   **Homepage Blog Slider:** Configurable, responsive.
*   **Sitemap & XML Generation:** Implemented with admin configuration.
*   **Contact Page:** Redesigned with two-column layout, map, social media, WhatsApp/Signal.

**User Preferences Adhered To:**
*   Simple confirmation message boxes over modals for deletes (Messages, Maintenance, Session Deletion).
*   Confirmation dialogs before permanent DB modifications (Maintenance).
*   Removal of modals/AJAX in favor of simpler implementations (Maintenance, Messages).
*   Darker text/backgrounds for readability (Dashboard).
*   Digital file management clarity improved with `display_name`.
*   Persistent UI States: Collapsible filter bar now remembers its visibility state across page loads (user preference for consistent UI behavior).

**Blog System:**
*   **"CODE" Post Type Implemented:**
*   Allows embedding and server-side execution of PHP, HTML, and JavaScript code directly within a blog post.
*   Database schema (`blog_posts` table) updated with `code_content` column and modified `post_type` CHECK constraint ([`includes/db_migrations/add_code_blog_post_type.php`](includes/db_migrations/add_code_blog_post_type.php:1)).
*   Admin form ([`templates/backend/blog_post_form.php`](templates/backend/blog_post_form.php:1)) updated with new type option and dedicated code input textarea.
*   Backend logic in [`admin.php`](admin.php:1) and [`includes/blog_functions.php`](includes/blog_functions.php:1) updated to handle saving and retrieving `code_content`.
*   Frontend template ([`templates/frontend/blog_post.php`](templates/frontend/blog_post.php:1)) updated to use `eval()` for rendering/executing `code_content`, with a prominent security warning and basic error handling.
*   **Security Note:** This feature is extremely high-risk due to the use of `eval()`.

## What's Left to Build / Enhance (Key Areas)

**User Preferences & General:**
*   Systematically remove `error_log`, `console.log`, comment lines; create/use automated cleanup scripts (ongoing).
*   Apply darker text colors for client names/emails, coupon input text (if not already fully covered).
*   Obfuscate `order_id` in URLs.
*   NIF field on checkout: 'PT' fixed prefix, users enter only numbers.

**UI/UX Requirements (General):**
*   Images with rounded corners.
*   Tables containing images to fill entire width.
*   Buttons in blog list to display text labels.
*   Product filtering by name, state, and digital status in admin.
*   Improve responsiveness of `index.php` category listings (eliminate horizontal scrollbars).

**Blog System (New Features):**
*   Full system with multiple content types, category structure (if existing needs expansion), configurable display settings, advanced SEO (beyond current), masonry layout, header menu integration.
*   Blog post SEO & Social fields: 'instant pre-fill' button for editing (if current pre-fill isn't "instant" on edit).
*   Blog posts: configurable link target behavior, images with optional author credit descriptions.

**Product & Order Management (Outstanding):**
*   Order tracking numbers and item lists on success pages (if not fully covered).
*   `estado` field translated to European Portuguese consistently (verify everywhere, including success pages).
*   Stock management functions to robustly distinguish all product types for stock handling (ensure current solution is fully comprehensive).

**Footer & Layout (Outstanding):**
*   Store name, social media icons, description above other footer sections; two-column full-width layout.
*   Justified store description text; icons before contact info; bullet points for placeholder page links (if not fully covered).
*   Digital product pages: display placeholder with slug 'ficheiros-digitais' under product image section.

**Privacy & Security (Outstanding):**
*   Email notifications to clients on data anonymization (proper formatting).
*   Backoffice 'Manutenção' setting: check/collect *expired download tokens* (verify existing cleanup covers this comprehensively or if new UI is needed). The new "Limpar Tokens de Download ATIVOS" option is distinct from cleaning *expired* ones.
*   Backoffice 'Manutenção' setting for *order access tokens*: existing cleanup for expired tokens. New option added for *active* tokens.

**Testing (From Active Context - Next Steps):**
*   **Thorough testing of newly added Token & Session Management:**
    *   Verify "Limpar Tokens de Download ATIVOS (Não Expirados)" functionality.
    *   Verify "Limpar Tokens de Acesso ATIVOS (Não Expirados)" functionality.
    *   Test "Gerir Sessões" page: list accuracy, cart summary parsing, session deletion, and associated download token deletion. Ensure no errors in `logs/error.log`.
*   **Thorough testing of Simple Product & Custom Field Add to Cart (Client & Server):**
    *   Verify client-side UI on [`templates/frontend/product_detail.php`](templates/frontend/product_detail.php:1) behaves correctly (stock, price, add-to-cart button state).
    *   Verify server-side validation in [`includes/ajax_handler.php`](includes/ajax_handler.php:287) correctly allows adding simple/digital products with custom fields without "select options" error.
    *   Ensure custom fields are correctly saved to the cart for these product types.
*   **Thorough testing of Digital Product functionality is paramount** after recent fixes.
*   Extensive testing for admin filters & pagination.
*   Thorough testing of enhanced order status handling for digital products.
*   Comprehensive testing of enhanced license management.
*   End-to-end testing of the entire digital products workflow.
*   Test placeholder links, custom fields cleanup, SEO, blog slider, sitemap, contact page.
*   **Thorough testing of "CODE" Blog Post Type:**
*   Verify correct execution of PHP, HTML, and JavaScript.
*   Test admin creation/editing workflow for "CODE" posts.
*   Confirm data integrity (other content fields are correctly nulled).
*   Check frontend security warnings and `eval()` error display/logging.

## Known Issues
1.  **Digital Products Testing Needs (as per "Next Steps"):**
    *   Complete end-to-end testing of the digital product workflow, including new `display_name` feature and file type association saving.
    *   Verify license expiration functionality works correctly.
    *   Test download limits to ensure they are properly enforced.
    *   Verify IP-based restrictions are working as expected.
    *   Test session-based security measures for downloads.
2.  **Digital Products Security Considerations (as per "Next Steps"):**
    *   Need to thoroughly test download protection mechanisms.
    *   Consider implementing additional encryption for sensitive files.
    *   Potential for unauthorized access if license validation is bypassed (ongoing vigilance).
    *   Need to verify proper file cleanup when products are deleted (ensure this is robust, especially with the new `digital_files` table structure).
    *   Consider implementing watermarking for PDF and image files.
3.  **Security Risk with "CODE" Blog Posts:** The use of `eval()` to execute code from the `code_content` field of "CODE" blog posts presents a severe security vulnerability (Remote Code Execution). This feature must be used with extreme caution and only by trusted administrators with thoroughly vetted code. It is not recommended for production environments without significant additional security hardening.
4.  **Session Cart Summary Parsing:** The current method for parsing cart content from session data in `get_all_sessions_data()` is a simplified heuristic. It might not be 100% accurate for all possible cart structures or if session data is unusually formatted. This is acceptable for a summary but not for critical operations.
5.  **Previous Digital Products Admin Interface Issues (Now Resolved):**
    *   Fixed the digital_products_list.php section to properly display digital products.
    *   Fixed the "Selecionar Existente" tab in digital_product_form.php to populate form fields when a file is selected.
    *   Fixed the button text to change to "Guardar definições" when an existing file is selected.
    *   Fixed database compatibility issues: `display_name` column missing, `digital_products_old` table reference error, file type associations not saving.

## Evolution of Project Decisions
1.  **Digital Products Implementation**:
    *   Supported three product types: regular, variation, and digital.
    *   **Key Decision**: Separated physical file information into a `digital_files` table (with `original_filename`, `display_name`, `file_path`, etc.). The `digital_products` table now links to `digital_files` via `digital_file_id` and no longer stores `file_path` directly. This improves file management and allows for user-friendly display names in the admin.
    *   Stored digital product files outside web root for security.
    *   Implemented license-based access control with states (waiting_payment, active, disabled, canceled).
    *   Made shipping address optional for digital-only orders.
    *   Used email notifications for license delivery and status changes.
    *   Implemented download tracking, limiting, IP tracking, and token-based validation.
    *   **Key Decision**: Automatic license activation/deactivation based on order status changes (processing, shipped/completed, cancelled, refunded), including removal of download tokens for cancelled/deactivated licenses. This ensures licenses accurately reflect order state.
    *   **Key Decision**: Separated license status from download validity in UI to avoid confusion (a license can be active but download period expired).
    *   **Key Decision (Fix)**: Addressed critical bugs preventing new digital file uploads (missing `display_name` column) and editing of digital product file type associations (due to incorrect table reference and then due to migration running too often). Ensured file type associations are correctly saved.
2.  **Blog System Enhancements**:
    *   **Key Decision (New Feature)**: Added a "CODE" blog post type as per user request, enabling direct embedding and server-side execution of PHP/HTML/JS via `eval()`. This decision was made with a strong emphasis on the associated security risks, and warnings have been implemented in the UI and documentation.
3.  **License Management Enhancements**:
    *   Added comprehensive admin tools (reset downloads, edit expiry, delete history, toggle activation) for flexibility.
    *   Used transaction management for operations affecting multiple tables.
    *   Implemented user-friendly confirmation messages.
4.  **Admin Interface Simplification (Modals/AJAX)**:
    *   **Key Decision**: Shifted away from complex AJAX/modal implementations for critical admin sections (Maintenance, Messages) towards simpler direct form submissions with page refreshes and JavaScript confirmations. This was driven by reliability issues and user preference. Admin sidebar navigation remains AJAX-driven, requiring careful handling for new sections.
5.  **Cookieless Operation**: Adopted as a fundamental requirement, influencing session management and visit tracking (e.g., `order_visits` table for context-aware order success page).
6.  **Stock Management**: Implemented detailed tracking (`stock_reduced`, `stock_restored` flags) and automated processes for non-digital products tied to order status changes.
7.  **Custom Fields**: Iteratively improved by fixing bugs, adding features (duplication, italicized descriptions), and addressing file management (automatic deletion of attachments). **Key Decision (Fix)**: Corrected both client-side UI updates (stock display) and server-side `add_to_cart` validation in [`includes/ajax_handler.php`](includes/ajax_handler.php:287) to properly handle simple products (regular/digital) with custom fields, ensuring they don't require variation selection and are added to cart correctly.
8.  **SEO & Social Media**: Adopted a consistent approach for products and blog posts, including pre-fill options and comprehensive metatag output.
9.  **User Feedback Prioritization**: Actively incorporated user preferences regarding UI (confirmations, no modals for deletes, readability, clearer file names, persistent filter bar state) and functionality.
10. **Coupon System**: Implemented with AJAX for applying codes on the cart page, followed by a page refresh for the checkout page, aligning with the preference for checkout refresh on coupon application while providing a smoother initial application experience.
11. **Database Migrations**: Recognized the need for careful management of database migration scripts to ensure they run once or are idempotent, especially for structural changes, to prevent data loss or unintended behavior. The recent digital product and "CODE" blog post type fixes/features highlighted the importance of this.
12. **Client-Side Enhancements**: Utilized `localStorage` for UI state persistence (filter bar visibility) and refined CSS utility classes for better responsive layout of fixed-position elements.
13. **Session Management & Token Cleanup (Admin)**: Added admin tools for clearing active (non-expired) download and order access tokens. Implemented a new admin section to list and delete active user sessions, including associated download tokens. Addressed AJAX loading and session data parsing issues for this new section.
14. **Current Phase**: Testing recently completed enhancements (simple product custom field handling, digital products, license management, admin filters, messages, maintenance, **new token and session management tools, "CODE" blog post type**), and planning next cycle of features and refinements based on user requirements.