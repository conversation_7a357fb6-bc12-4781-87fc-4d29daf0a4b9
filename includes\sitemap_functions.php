<?php

function ensure_sitemap_configs_table_exists(): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sitemap_configs'");
        $table_exists = $stmt->fetchColumn();

        if (!$table_exists) {

            
            $pdo->exec("CREATE TABLE sitemap_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL CHECK(type IN ('sitemap', 'google_merchant', 'custom', 'atom')),
                output_path TEXT NOT NULL,
                include_products INTEGER NOT NULL DEFAULT 1,
                include_blog INTEGER NOT NULL DEFAULT 1,
                include_pages INTEGER NOT NULL DEFAULT 1,
                custom_config_json TEXT,
                is_active INTEGER NOT NULL DEFAULT 1,
                include_regular_products INTEGER NOT NULL DEFAULT 1,
                include_variation_products INTEGER NOT NULL DEFAULT 1,
                include_digital_products INTEGER NOT NULL DEFAULT 1,
                last_generated TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            )");

            
            $default_data = [
                'name' => 'Sitemap Padrão',
                'type' => 'sitemap',
                'output_path' => 'sitemap.xml',
                'include_products' => 1,
                'include_blog' => 1,
                'include_pages' => 1,
                'is_active' => 1
            ];

            create_sitemap_config($default_data);
        }

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

function get_sitemap_configs(bool $active_only = false): array
{
    
    ensure_sitemap_configs_table_exists();

    $sql = "SELECT * FROM sitemap_configs";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY name ASC";

    return db_query($sql, [], false, true) ?: [];
}

function get_sitemap_config(int $id)
{
    return db_query("SELECT * FROM sitemap_configs WHERE id = :id", [':id' => $id], true);
}

function create_sitemap_config(array $data)
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO sitemap_configs (
            name,
            type,
            output_path,
            include_products,
            include_blog,
            include_pages,
            custom_config_json,
            is_active,
            include_regular_products,
            include_variation_products,
            include_digital_products,
            created_at,
            updated_at
        ) VALUES (
            :name,
            :type,
            :output_path,
            :include_products,
            :include_blog,
            :include_pages,
            :custom_config_json,
            :is_active,
            :include_regular_products,
            :include_variation_products,
            :include_digital_products,
            datetime('now', 'localtime'),
            datetime('now', 'localtime')
        )");

        $stmt->execute([
            ':name' => $data['name'],
            ':type' => $data['type'],
            ':output_path' => $data['output_path'],
            ':include_products' => $data['include_products'] ? 1 : 0,
            ':include_blog' => $data['include_blog'] ? 1 : 0,
            ':include_pages' => $data['include_pages'] ? 1 : 0,
            ':custom_config_json' => $data['custom_config_json'] ?? null,
            ':is_active' => $data['is_active'] ? 1 : 0,
            ':include_regular_products' => isset($data['include_regular_products']) ? ($data['include_regular_products'] ? 1 : 0) : 1,
            ':include_variation_products' => isset($data['include_variation_products']) ? ($data['include_variation_products'] ? 1 : 0) : 1,
            ':include_digital_products' => isset($data['include_digital_products']) ? ($data['include_digital_products'] ? 1 : 0) : 1
        ]);

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return false;
    }
}

function update_sitemap_config(int $id, array $data): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("UPDATE sitemap_configs SET
            name = :name,
            type = :type,
            output_path = :output_path,
            include_products = :include_products,
            include_blog = :include_blog,
            include_pages = :include_pages,
            custom_config_json = :custom_config_json,
            is_active = :is_active,
            include_regular_products = :include_regular_products,
            include_variation_products = :include_variation_products,
            include_digital_products = :include_digital_products,
            updated_at = datetime('now', 'localtime')
            WHERE id = :id
        ");

        return $stmt->execute([
            ':id' => $id,
            ':name' => $data['name'],
            ':type' => $data['type'],
            ':output_path' => $data['output_path'],
            ':include_products' => $data['include_products'] ? 1 : 0,
            ':include_blog' => $data['include_blog'] ? 1 : 0,
            ':include_pages' => $data['include_pages'] ? 1 : 0,
            ':custom_config_json' => $data['custom_config_json'] ?? null,
            ':is_active' => $data['is_active'] ? 1 : 0,
            ':include_regular_products' => isset($data['include_regular_products']) ? ($data['include_regular_products'] ? 1 : 0) : 1,
            ':include_variation_products' => isset($data['include_variation_products']) ? ($data['include_variation_products'] ? 1 : 0) : 1,
            ':include_digital_products' => isset($data['include_digital_products']) ? ($data['include_digital_products'] ? 1 : 0) : 1
        ]);
    } catch (PDOException $e) {
        return false;
    }
}

function delete_sitemap_config(int $id): bool
{
    return db_query("DELETE FROM sitemap_configs WHERE id = :id", [':id' => $id]) !== false;
}

function update_sitemap_last_generated(int $id): bool
{
    return db_query(
        "UPDATE sitemap_configs SET last_generated = datetime('now', 'localtime') WHERE id = :id",
        [':id' => $id]
    ) !== false;
}

function generate_standard_sitemap(array $config): array
{
    $site_url = get_site_url();
    $output_path = get_sitemap_output_path($config);

    
    $xml = new DOMDocument('1.0', 'UTF-8');
    $xml->formatOutput = true;

    
    $urlset = $xml->createElement('urlset');
    $urlset->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
    $xml->appendChild($urlset);

    
    add_url_to_sitemap($xml, $urlset, $site_url, date('Y-m-d'), 'weekly', '1.0');

    
    if ($config['include_products']) {
        $products = get_active_products();
        foreach ($products as $product) {
            $product_url = $site_url . '?product=' . $product['slug'];
            $lastmod = $product['updated_at'] ? date('Y-m-d', strtotime($product['updated_at'])) : date('Y-m-d');
            add_url_to_sitemap($xml, $urlset, $product_url, $lastmod, 'weekly', '0.8');
        }
    }

    
    if ($config['include_blog']) {
        $blog_posts = get_published_blog_posts();
        foreach ($blog_posts as $post) {
            $post_url = $site_url . '?view=blog_post&slug=' . $post['slug'];
            $lastmod = $post['updated_at'] ? date('Y-m-d', strtotime($post['updated_at'])) : date('Y-m-d');
            add_url_to_sitemap($xml, $urlset, $post_url, $lastmod, 'weekly', '0.7');
        }

        
        $blog_categories = get_active_blog_categories();
        foreach ($blog_categories as $category) {
            $category_url = $site_url . '?view=blog&category=' . $category['slug'];
            add_url_to_sitemap($xml, $urlset, $category_url, date('Y-m-d'), 'weekly', '0.6');
        }

        
        add_url_to_sitemap($xml, $urlset, $site_url . '?view=blog', date('Y-m-d'), 'daily', '0.8');
    }

    
    if ($config['include_pages']) {
        $pages = get_active_pages();
        foreach ($pages as $page) {
            $page_url = $site_url . '?page=' . $page['slug'];
            $lastmod = $page['updated_at'] ? date('Y-m-d', strtotime($page['updated_at'])) : date('Y-m-d');
            add_url_to_sitemap($xml, $urlset, $page_url, $lastmod, 'monthly', '0.5');
        }
    }

    
    $result = save_xml_file($xml, $output_path);

    if ($result['success']) {
        
        update_sitemap_last_generated($config['id']);
    }

    return $result;
}

function product_has_variations_with_stock(int $product_id): bool
{
    if ($product_id <= 0) {
        return false;
    }

    
    $sql = "SELECT COUNT(*) as count FROM product_variations
            WHERE product_id = :pid AND is_active = 1 AND stock > 0";

    $result = db_query($sql, [':pid' => $product_id], true);

    return ($result && isset($result['count']) && (int)$result['count'] > 0);
}

function is_product_in_stock(array $product): bool
{
    

    
    if (!empty($product['sku']) && $product['stock'] > 0) {
        return true;
    }

    
    $has_variations_with_stock = product_has_variations_with_stock($product['id']);

    return $has_variations_with_stock;
}

function generate_google_merchant_feed(array $config): array
{
    $site_url = get_site_url();
    $output_path = get_sitemap_output_path($config);
    $store_name = get_setting('store_name', 'My Store');
    $currency = get_setting('default_currency', 'EUR');

    
    $debug_info = [];

    
    $xml = new DOMDocument('1.0', 'UTF-8');
    $xml->formatOutput = true;

    
    $rss = $xml->createElement('rss');
    $rss->setAttribute('version', '2.0');
    $rss->setAttribute('xmlns:g', 'http://base.google.com/ns/1.0');
    $xml->appendChild($rss);

    
    $channel = $xml->createElement('channel');
    $rss->appendChild($channel);

    
    $channel->appendChild($xml->createElement('title', htmlspecialchars($store_name)));
    $channel->appendChild($xml->createElement('link', htmlspecialchars($site_url)));
    $channel->appendChild($xml->createElement('description', htmlspecialchars(get_setting('store_description', 'Product feed'))));

    
    if ($config['include_products']) {
        
        $product_type_filter = [];

        
        $include_regular = isset($config['include_regular_products']) ? (bool)$config['include_regular_products'] : true;
        $include_variation = isset($config['include_variation_products']) ? (bool)$config['include_variation_products'] : true;
        $include_digital = isset($config['include_digital_products']) ? (bool)$config['include_digital_products'] : true;

        
        $product_type_conditions = [];
        $params = [];

        if ($include_regular) {
            $product_type_conditions[] = "product_type = 'regular'";
        }

        if ($include_variation) {
            $product_type_conditions[] = "product_type = 'variation'";
        }

        if ($include_digital) {
            $product_type_conditions[] = "product_type = 'digital'";
        }

        
        if (empty($product_type_conditions)) {
            $products = [];
        } else {
            
            $sql = "SELECT * FROM products WHERE is_active = 1 AND (" . implode(" OR ", $product_type_conditions) . ")";
            $products = db_query($sql, $params, false, true) ?: [];
        }

        foreach ($products as $product) {
            
            if ($product['base_price'] <= 0) {
                continue;
            }

            
            $has_variations_query = "SELECT COUNT(*) as count FROM product_variations
                                    WHERE product_id = :pid AND is_active = 1 AND stock > 0";
            $has_variations_result = db_query($has_variations_query, [':pid' => $product['id']], true);
            $has_variations_with_stock = ($has_variations_result && isset($has_variations_result['count']) && (int)$has_variations_result['count'] > 0);

            
            $debug_info[] = [
                'product_id' => $product['id'],
                'name' => $product['name_pt'],
                'sku' => $product['sku'] ?? 'none',
                'stock' => $product['stock'] ?? '0',
                'has_variations_with_stock' => $has_variations_with_stock ? 'yes' : 'no',
                'variations_count' => $has_variations_result['count'] ?? '0'
            ];

            
            $in_stock = false;

            
            if (!empty($product['sku']) && $product['stock'] > 0) {
                $in_stock = true;
            }
            
            else if ($has_variations_with_stock) {
                $in_stock = true;
            }

            $item = $xml->createElement('item');
            $channel->appendChild($item);

            
            $item->appendChild($xml->createElement('g:id', htmlspecialchars($product['id'])));
            $item->appendChild($xml->createElement('g:title', htmlspecialchars($product['name_pt'])));
            $item->appendChild($xml->createElement('g:description', htmlspecialchars(strip_tags($product['description_pt']))));
            $item->appendChild($xml->createElement('g:link', htmlspecialchars($site_url . '?product=' . $product['slug'])));

            
            $image_url = get_product_main_image_url($product['id']);
            if ($image_url) {
                $item->appendChild($xml->createElement('g:image_link', htmlspecialchars($image_url)));
            }

            
            $item->appendChild($xml->createElement('g:price', number_format($product['base_price'], 2, '.', '') . ' ' . $currency));

            
            $availability = $in_stock ? 'in stock' : 'out of stock';
            $item->appendChild($xml->createElement('g:availability', $availability));

            
            if (!empty($product['sku'])) {
                $item->appendChild($xml->createElement('g:mpn', htmlspecialchars($product['sku'])));
            }

            
            $categories = get_product_categories_for_sitemap($product['id']);
            if (!empty($categories)) {
                $category_names = array_column($categories, 'name');
                $item->appendChild($xml->createElement('g:product_type', htmlspecialchars(implode(' > ', $category_names))));
            }
        }
    }

    
    file_put_contents('merchant_feed_debug.json', json_encode($debug_info, JSON_PRETTY_PRINT));

    
    $result = save_xml_file($xml, $output_path);

    if ($result['success']) {
        
        update_sitemap_last_generated($config['id']);
    }

    return $result;
}

function add_url_to_sitemap(DOMDocument $xml, DOMElement $urlset, string $loc, string $lastmod, string $changefreq, string $priority): void
{
    $url = $xml->createElement('url');
    $urlset->appendChild($url);

    $url->appendChild($xml->createElement('loc', htmlspecialchars($loc)));
    $url->appendChild($xml->createElement('lastmod', $lastmod));
    $url->appendChild($xml->createElement('changefreq', $changefreq));
    $url->appendChild($xml->createElement('priority', $priority));
}

function save_xml_file(DOMDocument $xml, string $output_path): array
{
    try {
        
        $dir = dirname($output_path);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                return [
                    'success' => false,
                    'message' => "Failed to create directory: {$dir}"
                ];
            }
        }

        
        if (!is_writable($dir)) {
            return [
                'success' => false,
                'message' => "Directory is not writable: {$dir}"
            ];
        }

        
        if ($xml->save($output_path) === false) {
            return [
                'success' => false,
                'message' => "Failed to save XML file: {$output_path}"
            ];
        }

        return [
            'success' => true,
            'message' => "XML file saved successfully: {$output_path}"
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Error saving XML file: " . $e->getMessage()
        ];
    }
}

function get_sitemap_output_path(array $config): string
{
    
    if (preg_match('/^[A-Z]:\\\\|^\//', $config['output_path'])) {
        return $config['output_path'];
    }

    
    return PROJECT_ROOT . '/' . ltrim($config['output_path'], '/\\');
}

function get_site_url(): string
{
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domain = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    $path = ($path === '/' || $path === '\\') ? '' : $path;

    return $protocol . $domain . $path . '/';
}

function get_active_products(): array
{
    return db_query("SELECT * FROM products WHERE is_active = 1", [], false, true) ?: [];
}

function get_published_blog_posts(): array
{
    return db_query("SELECT * FROM blog_posts WHERE is_published = 1", [], false, true) ?: [];
}

function get_active_blog_categories(): array
{
    return db_query("SELECT * FROM blog_categories WHERE is_active = 1", [], false, true) ?: [];
}

function get_active_pages(): array
{
    return db_query("SELECT * FROM pages WHERE is_active = 1", [], false, true) ?: [];
}

function get_product_main_image_url(int $product_id): ?string
{
    $image = db_query(
        "SELECT filename FROM product_images WHERE product_id = :pid ORDER BY sort_order ASC LIMIT 1",
        [':pid' => $product_id],
        true
    );

    if ($image && !empty($image['filename'])) {
        return get_product_image_url($image['filename']);
    }

    return null;
}

function get_product_categories_for_sitemap(int $product_id): array
{
    return db_query(
        "SELECT c.* FROM categories c
        JOIN product_categories pc ON c.id = pc.category_id
        WHERE pc.product_id = :pid AND c.is_active = 1",
        [':pid' => $product_id],
        false,
        true
    ) ?: [];
}

function update_robots_txt(array $sitemap_configs = []): array
{
    
    if (empty($sitemap_configs)) {
        $sitemap_configs = get_sitemap_configs(true);
    }

    
    $standard_sitemaps = array_filter($sitemap_configs, function($config) {
        return $config['type'] === 'sitemap';
    });

    if (empty($standard_sitemaps)) {
        return [
            'success' => false,
            'message' => "Não existem configurações de sitemap padrão ativas."
        ];
    }

    
    $robots_path = PROJECT_ROOT . '/robots.txt';

    
    $robots_content = '';
    if (file_exists($robots_path)) {
        $robots_content = file_get_contents($robots_path);

        
        $robots_content = preg_replace('/^Sitemap:.*$/m', '', $robots_content);

        
        $robots_content = preg_replace('/\n\s*\n/', "\n\n", $robots_content);

        
        if (substr($robots_content, -1) !== "\n") {
            $robots_content .= "\n";
        }

        
        if (substr($robots_content, -2) !== "\n\n") {
            $robots_content .= "\n";
        }
    } else {
        
        $robots_content = "User-agent: *\nAllow: /\n\n";
    }

    
    $site_url = get_site_url();
    foreach ($standard_sitemaps as $sitemap) {
        $sitemap_path = $sitemap['output_path'];

        
        if (preg_match('/^[A-Z]:\\\\|^\//', $sitemap_path)) {
            
            $filename = basename($sitemap_path);
            $sitemap_url = $site_url . $filename;
        } else {
            
            $sitemap_url = $site_url . ltrim($sitemap_path, '/\\');
        }

        $robots_content .= "Sitemap: {$sitemap_url}\n";
    }

    
    try {
        if (file_put_contents($robots_path, $robots_content) === false) {
            return [
                'success' => false,
                'message' => "Falha ao atualizar o ficheiro robots.txt."
            ];
        }

        return [
            'success' => true,
            'message' => "Ficheiro robots.txt atualizado com sucesso."
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Erro ao atualizar o ficheiro robots.txt: " . $e->getMessage()
        ];
    }
}

function generate_sitemap(int $config_id): array
{
    $config = get_sitemap_config($config_id);
    if (!$config) {
        return [
            'success' => false,
            'message' => "Configuração de sitemap não encontrada"
        ];
    }

    $result = [];

    switch ($config['type']) {
        case 'sitemap':
            $result = generate_standard_sitemap($config);

            
            if ($result['success'] && $config['is_active']) {
                $robots_result = update_robots_txt();
                if (!$robots_result['success']) {
                    
                    $result['message'] .= " Aviso: " . $robots_result['message'];
                }
            }
            break;

        case 'google_merchant':
            $result = generate_google_merchant_feed($config);
            break;

        case 'atom':
            $result = generate_atom_feed($config);
            break;

        case 'custom':
            
            $custom_config = json_decode($config['custom_config_json'], true);
            if (!$custom_config) {
                $result = [
                    'success' => false,
                    'message' => "Configuração personalizada inválida. Verifique o formato JSON."
                ];
            } else {
                $result = generate_custom_xml($config, $custom_config);
            }
            break;

        default:
            $result = [
                'success' => false,
                'message' => "Tipo de sitemap desconhecido: {$config['type']}"
            ];
    }

    return $result;
}

function generate_all_active_sitemaps(): array
{
    $active_configs = get_sitemap_configs(true);

    if (empty($active_configs)) {
        return [
            'success' => false,
            'message' => "Não existem configurações de sitemap ativas.",
            'details' => []
        ];
    }

    $results = [];
    $success_count = 0;
    $error_count = 0;

    foreach ($active_configs as $config) {
        $result = generate_sitemap($config['id']);
        $results[$config['name']] = $result;

        if ($result['success']) {
            $success_count++;
        } else {
            $error_count++;
        }
    }

    
    $robots_result = update_robots_txt($active_configs);

    $message = "Geração concluída: {$success_count} com sucesso, {$error_count} com erros.";
    if (!$robots_result['success']) {
        $message .= " Aviso: " . $robots_result['message'];
    }

    return [
        'success' => ($error_count === 0),
        'message' => $message,
        'details' => $results
    ];
}

function generate_custom_xml(array $config, array $custom_config): array
{
    $site_url = get_site_url();
    $output_path = get_sitemap_output_path($config);

    try {
        
        $xml = new DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;

        
        $root_element = $custom_config['root_element'] ?? 'root';
        $root_attributes = $custom_config['root_attributes'] ?? [];

        
        $root = $xml->createElement($root_element);
        $xml->appendChild($root);

        
        foreach ($root_attributes as $attr_name => $attr_value) {
            $root->setAttribute($attr_name, $attr_value);
        }

        
        if (isset($custom_config['items']) && is_array($custom_config['items'])) {
            process_custom_xml_items($xml, $root, $custom_config['items'], $site_url);
        }

        
        if (isset($custom_config['custom_queries']) && is_array($custom_config['custom_queries'])) {
            foreach ($custom_config['custom_queries'] as $query_config) {
                process_custom_xml_query($xml, $root, $query_config, $site_url);
            }
        }

        
        $result = save_xml_file($xml, $output_path);

        if ($result['success']) {
            
            update_sitemap_last_generated($config['id']);
        }

        return $result;
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Erro ao gerar XML personalizado: " . $e->getMessage()
        ];
    }
}

function process_custom_xml_items(DOMDocument $xml, DOMElement $parent, array $items, string $site_url): void
{
    foreach ($items as $item_config) {
        if (!isset($item_config['element'])) {
            continue;
        }

        $element_name = $item_config['element'];
        $element = $xml->createElement($element_name);
        $parent->appendChild($element);

        
        if (isset($item_config['attributes']) && is_array($item_config['attributes'])) {
            foreach ($item_config['attributes'] as $attr_name => $attr_value) {
                $element->setAttribute($attr_name, $attr_value);
            }
        }

        
        if (isset($item_config['content'])) {
            $content = $item_config['content'];

            
            $content = str_replace('{site_url}', $site_url, $content);
            $content = str_replace('{date}', date('Y-m-d'), $content);

            $element->appendChild($xml->createTextNode($content));
        }

        
        if (isset($item_config['children']) && is_array($item_config['children'])) {
            process_custom_xml_items($xml, $element, $item_config['children'], $site_url);
        }
    }
}

function generate_atom_feed(array $config): array
{
    $site_url = get_site_url();
    $output_path = get_sitemap_output_path($config);
    $store_name = get_setting('store_name', 'My Store');
    $store_description = get_setting('store_description', 'Product feed for ' . $store_name);
    $author_name = get_setting('sitemap_atom_author_name', 'Joao Cesar Silva'); 

    $xml = new DOMDocument('1.0', 'UTF-8');
    $xml->formatOutput = true;

    $feed = $xml->createElementNS('http://www.w3.org/2005/Atom', 'feed');
    
    $xml->appendChild($feed);

    $feed->appendChild($xml->createElement('title', htmlspecialchars($store_name . ' - Atom Feed')));
    $link_self = $xml->createElement('link');
    $link_self->setAttribute('href', htmlspecialchars($site_url . $config['output_path']));
    $link_self->setAttribute('rel', 'self');
    $feed->appendChild($link_self);

    $link_alternate = $xml->createElement('link');
    $link_alternate->setAttribute('href', htmlspecialchars($site_url));
    $feed->appendChild($link_alternate);

    $feed->appendChild($xml->createElement('id', htmlspecialchars($site_url))); 
    $feed->appendChild($xml->createElement('updated', date('c'))); 
    $feed->appendChild($xml->createElement('subtitle', htmlspecialchars($store_description)));

    $author = $xml->createElement('author');
    $author->appendChild($xml->createElement('name', htmlspecialchars($author_name)));
    $feed->appendChild($author);

    if ($config['include_products']) {
        $product_type_conditions = [];
        if (isset($config['include_regular_products']) && $config['include_regular_products']) {
            $product_type_conditions[] = "product_type = 'regular'";
        }
        if (isset($config['include_variation_products']) && $config['include_variation_products']) {
            
            
            
            $product_type_conditions[] = "product_type = 'variation'";
        }
        if (isset($config['include_digital_products']) && $config['include_digital_products']) {
            $product_type_conditions[] = "product_type = 'digital'";
        }

        $products_sql = "SELECT * FROM products WHERE is_active = 1";
        if (!empty($product_type_conditions)) {
            $products_sql .= " AND (" . implode(" OR ", $product_type_conditions) . ")";
        }
        $products = db_query($products_sql, [], false, true) ?: [];

        foreach ($products as $product) {
            if ($product['base_price'] <= 0) continue; 

            $entry = $xml->createElement('entry');
            $feed->appendChild($entry);

            $entry->appendChild($xml->createElement('title', htmlspecialchars($product['name_pt'])));
            $product_url = $site_url . 'index.php?product=' . $product['slug']; 
            $entry->appendChild($xml->createElement('link', htmlspecialchars($product_url)));
            $entry->appendChild($xml->createElement('id', htmlspecialchars($product_url))); 

            $updated_at_timestamp = !empty($product['updated_at']) ? strtotime($product['updated_at']) : time();
            $updated_at = date('c', $updated_at_timestamp === false ? time() : $updated_at_timestamp);

            $published_at_timestamp = !empty($product['created_at']) ? strtotime($product['created_at']) : time();
            $published_at = date('c', $published_at_timestamp === false ? time() : $published_at_timestamp);
            
            $entry->appendChild($xml->createElement('updated', $updated_at));
            $entry->appendChild($xml->createElement('published', $published_at));

            $content_text = !empty($product['description_pt']) ? strip_tags($product['description_pt']) : 'N/A';
            $content = $xml->createElement('content', htmlspecialchars($content_text));
            $content->setAttribute('type', 'text');
            $entry->appendChild($content);

            
            $summary_text = !empty($product['description_pt']) ? substr(strip_tags($product['description_pt']), 0, 200) . '...' : 'N/A';
            $summary = $xml->createElement('summary', htmlspecialchars($summary_text));
            $summary->setAttribute('type', 'text');
            $entry->appendChild($summary);

            $categories = get_product_categories_for_sitemap($product['id']);
            if (!empty($categories)) {
                foreach ($categories as $category_data) {
                    $category_element = $xml->createElement('category');
                    $category_element->setAttribute('term', htmlspecialchars($category_data['name']));
                    
                    
                    $entry->appendChild($category_element);
                }
            }
            
            
            $image_url = get_product_main_image_url($product['id']);
            if (!empty($image_url)) {
                $mime_type = guess_mime_type_from_url($image_url);
                if (!empty($mime_type)) {
                    $enclosure_link = $xml->createElement('link');
                    $enclosure_link->setAttribute('rel', 'enclosure');
                    $enclosure_link->setAttribute('type', $mime_type);
                    $enclosure_link->setAttribute('href', htmlspecialchars($image_url));
                    $entry->appendChild($enclosure_link);
                }
            }
        }
    }

    
    if ($config['include_blog']) {
        $blog_posts = get_published_blog_posts();
        foreach ($blog_posts as $post) {
            $entry = $xml->createElement('entry');
            $feed->appendChild($entry);

            $entry->appendChild($xml->createElement('title', htmlspecialchars($post['title'])));
            $post_url = $site_url . 'index.php?view=blog_post&slug=' . $post['slug'];
            $entry->appendChild($xml->createElement('link', htmlspecialchars($post_url)));
            $entry->appendChild($xml->createElement('id', htmlspecialchars($post_url)));

            $updated_at_timestamp = !empty($post['updated_at']) ? strtotime($post['updated_at']) : time();
            $updated_at = date('c', $updated_at_timestamp === false ? time() : $updated_at_timestamp);
            
            $published_at_timestamp = !empty($post['published_at']) ? strtotime($post['published_at']) : (!empty($post['created_at']) ? strtotime($post['created_at']) : time());
            $published_at = date('c', $published_at_timestamp === false ? time() : $published_at_timestamp);

            $entry->appendChild($xml->createElement('updated', $updated_at));
            $entry->appendChild($xml->createElement('published', $published_at));
            
            $content_text = 'N/A';
            if ($post['post_type'] === 'article' && !empty($post['content'])) {
                $content_text = strip_tags($post['content']);
            } elseif ($post['post_type'] === 'link' && !empty($post['link_description'])) {
                $content_text = strip_tags($post['link_description']);
            } elseif ($post['post_type'] === 'CODE' && !empty($post['code_content'])) {
                $content_text = 'Executable code content.'; 
            }

            $content_element = $xml->createElement('content', htmlspecialchars($content_text));
            $content_element->setAttribute('type', 'text');
            $entry->appendChild($content_element);

            $summary_text = substr($content_text, 0, 200) . (strlen($content_text) > 200 ? '...' : '');
            $summary = $xml->createElement('summary', htmlspecialchars($summary_text));
            $summary->setAttribute('type', 'text');
            $entry->appendChild($summary);

            
            $page_image_url = '';
            if (!empty($page['og_image'])) {
                $page_image_url = $page['og_image'];
            } elseif (!empty($page['twitter_image'])) {
                $page_image_url = $page['twitter_image'];
            }
            
            if (!empty($page_image_url)) {
                
                if (!filter_var($page_image_url, FILTER_VALIDATE_URL)) {
                    $page_image_url = $site_url . $page_image_url;
                }
                $mime_type = guess_mime_type_from_url($page_image_url);
                if (!empty($mime_type)) {
                    $enclosure = $xml->createElement('link');
                    $enclosure->setAttribute('rel', 'enclosure');
                    $enclosure->setAttribute('type', $mime_type);
                    $enclosure->setAttribute('href', htmlspecialchars($page_image_url));
                    $entry->appendChild($enclosure);
                }
            }

            $blog_categories = get_blog_post_categories_for_sitemap($post['id']);
            if (!empty($blog_categories)) {
                foreach ($blog_categories as $category_data) {
                    $category_element = $xml->createElement('category');
                    $category_element->setAttribute('term', htmlspecialchars($category_data['name']));
                    $entry->appendChild($category_element);
                }
            }

            
            if (!empty($post['image_path'])) {
                $image_url = $site_url . $post['image_path'];
                $mime_type = guess_mime_type_from_url($image_url);
                if (!empty($mime_type)) {
                    $enclosure = $xml->createElement('link');
                    $enclosure->setAttribute('rel', 'enclosure');
                    $enclosure->setAttribute('type', $mime_type);
                    $enclosure->setAttribute('href', htmlspecialchars($image_url));
                    $entry->appendChild($enclosure);
                }
            }
        }
    }

    
    if ($config['include_pages']) {
        $pages = get_active_pages();
        foreach ($pages as $page) {
            $entry = $xml->createElement('entry');
            $feed->appendChild($entry);

            $entry->appendChild($xml->createElement('title', htmlspecialchars($page['title'] ?? '')));
            $page_url = $site_url . 'index.php?page=' . $page['slug'];
            $entry->appendChild($xml->createElement('link', htmlspecialchars($page_url)));
            $entry->appendChild($xml->createElement('id', htmlspecialchars($page_url)));

            $updated_at_timestamp = !empty($page['updated_at']) ? strtotime($page['updated_at']) : time();
            $updated_at = date('c', $updated_at_timestamp === false ? time() : $updated_at_timestamp);

            $published_at_timestamp = !empty($page['created_at']) ? strtotime($page['created_at']) : time();
            $published_at = date('c', $published_at_timestamp === false ? time() : $published_at_timestamp);

            $entry->appendChild($xml->createElement('updated', $updated_at));
            $entry->appendChild($xml->createElement('published', $published_at));
            
            $content_text = !empty($page['content']) ? strip_tags($page['content']) : 'N/A';
            $content_element = $xml->createElement('content', htmlspecialchars($content_text));
            $content_element->setAttribute('type', 'text');
            $entry->appendChild($content_element);

            $summary_text = substr($content_text, 0, 200) . (strlen($content_text) > 200 ? '...' : '');
            $summary = $xml->createElement('summary', htmlspecialchars($summary_text));
            $summary->setAttribute('type', 'text');
            $entry->appendChild($summary);

            
            $page_image_url = '';
            if (!empty($page['og_image'])) {
                $page_image_url = $page['og_image'];
            } elseif (!empty($page['twitter_image'])) {
                $page_image_url = $page['twitter_image'];
            }
            
            if (!empty($page_image_url)) {
                
                if (!filter_var($page_image_url, FILTER_VALIDATE_URL)) {
                    $page_image_url = $site_url . $page_image_url;
                }
                $mime_type = guess_mime_type_from_url($page_image_url);
                if (!empty($mime_type)) {
                    $enclosure = $xml->createElement('link');
                    $enclosure->setAttribute('rel', 'enclosure');
                    $enclosure->setAttribute('type', $mime_type);
                    $enclosure->setAttribute('href', htmlspecialchars($page_image_url));
                    $entry->appendChild($enclosure);
                }
            }
        }
    }

    $result = save_xml_file($xml, $output_path);
    if ($result['success']) {
        update_sitemap_last_generated($config['id']);
    } else {
        
    }
    return $result;
}

function get_blog_post_categories_for_sitemap(int $post_id): array
{
    return db_query(
        "SELECT c.* FROM blog_categories c
        JOIN blog_post_categories bpc ON c.id = bpc.category_id
        WHERE bpc.post_id = :pid AND c.is_active = 1",
        [':pid' => $post_id],
        false,
        true
    ) ?: [];
}

function guess_mime_type_from_url(?string $url): string
{
    if (empty($url)) {
        return ''; 
    }
    $ext = strtolower(pathinfo($url, PATHINFO_EXTENSION));
    switch ($ext) {
        case 'jpg':
        case 'jpeg':
            return 'image/jpeg';
        case 'png':
            return 'image/png';
        case 'gif':
            return 'image/gif';
        case 'webp':
            return 'image/webp';
        default:
            return 'application/octet-stream'; 
    }
}

function process_custom_xml_query(DOMDocument $xml, DOMElement $parent, array $query_config, string $site_url): void
{
    if (!isset($query_config['sql']) || !isset($query_config['item_element'])) {
        return;
    }

    $sql = $query_config['sql'];
    $item_element = $query_config['item_element'];
    $params = $query_config['params'] ?? [];

    
    $results = db_query($sql, $params, false, true);
    if (!$results) {
        return;
    }

    
    foreach ($results as $row) {
        $element = $xml->createElement($item_element);
        $parent->appendChild($element);

        
        if (isset($query_config['fields']) && is_array($query_config['fields'])) {
            foreach ($query_config['fields'] as $field_config) {
                if (!isset($field_config['name']) || !isset($field_config['column'])) {
                    continue;
                }

                $field_name = $field_config['name'];
                $column = $field_config['column'];

                
                if (!isset($row[$column])) {
                    continue;
                }

                $value = $row[$column];

                
                if (isset($field_config['transform'])) {
                    switch ($field_config['transform']) {
                        case 'url':
                            $value = $site_url . $value;
                            break;
                        case 'date':
                            $value = date('Y-m-d', strtotime($value));
                            break;
                        case 'html_escape':
                            $value = htmlspecialchars($value);
                            break;
                    }
                }
                
                $field_element = $xml->createElement($field_name);
                $field_element->appendChild($xml->createTextNode($value));
                $element->appendChild($field_element);
            }
        }
    }
}