<?php

if (!isset($product_images_categorized)) {
    echo "<p>Erro: Dados das imagens não disponíveis.</p>";
    return;
}

$categorized_images = $product_images_categorized;
$page_title = $page_title ?? 'Gerir Imagens de Produtos';
$active_tab = $active_tab ?? 'jpeg-to-webp';

function get_product_image_url_template($filename, $product_slug) {
    return "/public/assets/images/products/" . $filename;
}

function get_format_badge($filename) {
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $badges = [
        'jpg' => '<span class="badge bg-primary">JPG</span>',
        'jpeg' => '<span class="badge bg-primary">JPEG</span>',
        'png' => '<span class="badge bg-info">PNG</span>',
        'webp' => '<span class="badge bg-success">WebP</span>'
    ];
    return $badges[$ext] ?? '<span class="badge bg-secondary">' . strtoupper($ext) . '</span>';
}

function build_pagination_url($base_params, $page, $tab_param) {
    $params = $base_params;
    $params[$tab_param] = $page;
    return 'admin.php?' . http_build_query($params);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-3">Gerir Imagens de Produtos</h1>
            
            <!-- Flash Messages -->
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-<?= $_SESSION['flash_type'] ?> alert-dismissible fade show" role="alert">
                    <?= $_SESSION['flash_message'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
            <?php endif; ?>
            
            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                            Filtros
                        </button>
                    </h5>
                </div>
                <div class="collapse <?= (!empty($product_name_filter) || !empty($sort_option)) ? 'show' : '' ?>" id="filterCollapse">
                    <div class="card-body">
                        <form method="GET" action="admin.php">
                            <input type="hidden" name="section" value="products">
                            <input type="hidden" name="action" value="images">
                            <input type="hidden" name="tab" value="<?= htmlspecialchars($active_tab) ?>">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="product_name" class="form-label">Nome do Produto</label>
                                    <input type="text" class="form-control" id="product_name" name="product_name" value="<?= htmlspecialchars($product_name_filter ?? '') ?>" placeholder="Pesquisar por nome...">
                                </div>
                                <div class="col-md-4">
                                    <label for="sort" class="form-label">Ordenar por</label>
                                    <select class="form-select" id="sort" name="sort">
                                        <option value="product_name" <?= ($sort_option ?? 'product_name') === 'product_name' ? 'selected' : '' ?>>Nome do Produto</option>
                                        <option value="filename" <?= ($sort_option ?? '') === 'filename' ? 'selected' : '' ?>>Nome do Ficheiro</option>
                                        <option value="recent" <?= ($sort_option ?? '') === 'recent' ? 'selected' : '' ?>>Mais Recentes</option>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                                    <a href="admin.php?section=products&action=images&tab=<?= htmlspecialchars($active_tab) ?>" class="btn btn-secondary">Limpar</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs mb-4" id="imagesTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?= $active_tab === 'jpeg-to-webp' ? 'active' : '' ?>" 
                       href="admin.php?section=products&action=images&tab=jpeg-to-webp<?= !empty($product_name_filter) ? '&product_name=' . urlencode($product_name_filter) : '' ?><?= !empty($sort_option) ? '&sort=' . urlencode($sort_option) : '' ?>">
                        JPEG/PNG → WebP
                        <?php if (isset($jpeg_total_images) && $jpeg_total_images > 0): ?>
                            <span class="badge bg-primary ms-1"><?= $jpeg_total_images ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?= $active_tab === 'webp-to-jpeg' ? 'active' : '' ?>" 
                       href="admin.php?section=products&action=images&tab=webp-to-jpeg<?= !empty($product_name_filter) ? '&product_name=' . urlencode($product_name_filter) : '' ?><?= !empty($sort_option) ? '&sort=' . urlencode($sort_option) : '' ?>">
                        WebP → JPEG
                        <?php if (isset($webp_total_images) && $webp_total_images > 0): ?>
                            <span class="badge bg-success ms-1"><?= $webp_total_images ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="imagesTabContent">
                <!-- JPEG/PNG to WebP Tab -->
                <div class="tab-pane fade <?= $active_tab === 'jpeg-to-webp' ? 'show active' : '' ?>" id="jpeg-to-webp" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Imagens JPEG/PNG (Para converter para WebP)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($jpeg_images)) : ?>
                                <p>Nenhuma imagem JPEG ou PNG encontrada.</p>
                            <?php else : ?>
                                <form action="admin.php?section=products&action=convert_image&<?php echo get_session_id_param(); ?>" method="post" onsubmit="return confirm('Tem certeza que deseja converter as imagens selecionadas para WebP?');">
                                    <input type="hidden" name="target_format" value="webp">
                                    <input type="hidden" name="form_token" value="<?php echo generate_csrf_token(); ?>">
                                    <div class="mb-3">
                                        <label for="quality_webp" class="form-label">Qualidade WebP (0-100, 80 recomendado):</label>
                                        <input type="number" class="form-control" id="quality_webp" name="quality" value="80" min="0" max="100" required>
                                    </div>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th><input type="checkbox" id="select_all_jpeg"></th>
                                                <th>Imagem</th>
                                                <th>Produto</th>
                                                <th>Formato</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($jpeg_images as $img) : ?>
                                                <tr>
                                                    <td><input type="checkbox" name="image_ids[]" value="<?php echo $img['id']; ?>" class="jpeg_checkbox"></td>
                                                    <td><img src="<?php echo htmlspecialchars(get_product_image_url_template($img['filename'], $img['product_slug'])); ?>" alt="<?php echo htmlspecialchars($img['alt_text'] ?? pathinfo($img['filename'], PATHINFO_FILENAME)); ?>" style="max-width: 100px; max-height: 100px;"></td>
                                                    <td><a href="admin.php?section=products&action=edit&id=<?php echo $img['product_id']; ?>&<?php echo get_session_id_param(); ?>"><?php echo htmlspecialchars($img['product_name']); ?></a></td>
                                                    <td><?php echo get_format_badge($img['filename']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <button type="submit" class="btn btn-primary">Converter Selecionadas para WebP</button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- JPEG Pagination -->
                    <?php if (isset($jpeg_total_pages) && $jpeg_total_pages > 1): ?>
                        <div class="mt-4">
                            <nav aria-label="Navegação de páginas JPEG/PNG">
                                <ul class="pagination justify-content-center">
                                    <?php
                                    $jpeg_current_page = $jpeg_current_page ?? 1;
                                    
                                    
                                    $jpeg_query_params = [];
                                    if (!empty($product_name_filter)) $jpeg_query_params['product_name'] = $product_name_filter;
                                    if (!empty($sort_option)) $jpeg_query_params['sort'] = $sort_option;
                                    $jpeg_query_params['section'] = 'products';
                                    $jpeg_query_params['action'] = 'images';
                                    $jpeg_query_params['tab'] = 'jpeg-to-webp';
                                    
                                    $jpeg_base_query = http_build_query($jpeg_query_params);
                                    
                                    
                                    if ($jpeg_current_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $jpeg_base_query ?>&jpeg_p=<?= $jpeg_current_page - 1 ?>" aria-label="Anterior">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-label="Anterior">
                                                <span aria-hidden="true">&laquo;</span>
                                            </span>
                                        </li>
                                    <?php endif;
                                    
                                    
                                    $jpeg_start_page = max(1, $jpeg_current_page - 2);
                                    $jpeg_end_page = min($jpeg_total_pages, $jpeg_current_page + 2);
                                    
                                    
                                    if ($jpeg_start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $jpeg_base_query ?>&jpeg_p=1">1</a>
                                        </li>
                                        <?php if ($jpeg_start_page > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif;
                                    endif;
                                    
                                    
                                    for ($i = $jpeg_start_page; $i <= $jpeg_end_page; $i++): ?>
                                        <li class="page-item <?= $i == $jpeg_current_page ? 'active' : '' ?>">
                                            <?php if ($i == $jpeg_current_page): ?>
                                                <span class="page-link"><?= $i ?></span>
                                            <?php else: ?>
                                                <a class="page-link" href="admin.php?<?= $jpeg_base_query ?>&jpeg_p=<?= $i ?>"><?= $i ?></a>
                                            <?php endif; ?>
                                        </li>
                                    <?php endfor;
                                    
                                    
                                    if ($jpeg_end_page < $jpeg_total_pages): ?>
                                        <?php if ($jpeg_end_page < $jpeg_total_pages - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $jpeg_base_query ?>&jpeg_p=<?= $jpeg_total_pages ?>"><?= $jpeg_total_pages ?></a>
                                        </li>
                                    <?php endif;
                                    
                                    
                                    if ($jpeg_current_page < $jpeg_total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $jpeg_base_query ?>&jpeg_p=<?= $jpeg_current_page + 1 ?>" aria-label="Próxima">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-label="Próxima">
                                                <span aria-hidden="true">&raquo;</span>
                                            </span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            
                            <!-- JPEG Results info -->
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    Mostrando <?= min(($jpeg_current_page - 1) * 20 + 1, $jpeg_total_images ?? 0) ?> a <?= min($jpeg_current_page * 20, $jpeg_total_images ?? 0) ?> de <?= $jpeg_total_images ?? 0 ?> imagens JPEG/PNG
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- WebP to JPEG Tab -->
                <div class="tab-pane fade <?= $active_tab === 'webp-to-jpeg' ? 'show active' : '' ?>" id="webp-to-jpeg" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Imagens WebP (Para converter para JPEG)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($webp_images)) : ?>
                                <p>Nenhuma imagem WebP encontrada.</p>
                            <?php else : ?>
                                <form action="admin.php?section=products&action=convert_image&<?php echo get_session_id_param(); ?>" method="post" onsubmit="return confirm('Tem certeza que deseja converter as imagens selecionadas para JPEG?');">
                                    <input type="hidden" name="target_format" value="jpg">
                                    <input type="hidden" name="form_token" value="<?php echo generate_csrf_token(); ?>">
                                    <div class="mb-3">
                                        <label for="quality_jpg" class="form-label">Qualidade JPEG (0-100, 85 recomendado):</label>
                                        <input type="number" class="form-control" id="quality_jpg" name="quality" value="85" min="0" max="100" required>
                                    </div>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th><input type="checkbox" id="select_all_webp"></th>
                                                <th>Imagem</th>
                                                <th>Produto</th>
                                                <th>Formato</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($webp_images as $img) : ?>
                                                <tr>
                                                    <td><input type="checkbox" name="image_ids[]" value="<?php echo $img['id']; ?>" class="webp_checkbox"></td>
                                                    <td><img src="<?php echo htmlspecialchars(get_product_image_url_template($img['filename'], $img['product_slug'])); ?>" alt="<?php echo htmlspecialchars($img['alt_text'] ?? pathinfo($img['filename'], PATHINFO_FILENAME)); ?>" style="max-width: 100px; max-height: 100px;"></td>
                                                    <td><a href="admin.php?section=products&action=edit&id=<?php echo $img['product_id']; ?>&<?php echo get_session_id_param(); ?>"><?php echo htmlspecialchars($img['product_name']); ?></a></td>
                                                    <td><?php echo get_format_badge($img['filename']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <button type="submit" class="btn btn-primary">Converter Selecionadas para JPEG</button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- WebP Pagination -->
                    <?php if (isset($webp_total_pages) && $webp_total_pages > 1): ?>
                        <div class="mt-4">
                            <nav aria-label="Navegação de páginas WebP">
                                <ul class="pagination justify-content-center">
                                    <?php
                                    $webp_current_page = $webp_current_page ?? 1;
                                    
                                    
                                    $webp_query_params = [];
                                    if (!empty($product_name_filter)) $webp_query_params['product_name'] = $product_name_filter;
                                    if (!empty($sort_option)) $webp_query_params['sort'] = $sort_option;
                                    $webp_query_params['section'] = 'products';
                                    $webp_query_params['action'] = 'images';
                                    $webp_query_params['tab'] = 'webp-to-jpeg';
                                    
                                    $webp_base_query = http_build_query($webp_query_params);
                                    
                                    
                                    if ($webp_current_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $webp_base_query ?>&webp_p=<?= $webp_current_page - 1 ?>" aria-label="Anterior">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-label="Anterior">
                                                <span aria-hidden="true">&laquo;</span>
                                            </span>
                                        </li>
                                    <?php endif;
                                    
                                    
                                    $webp_start_page = max(1, $webp_current_page - 2);
                                    $webp_end_page = min($webp_total_pages, $webp_current_page + 2);
                                    
                                    
                                    if ($webp_start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $webp_base_query ?>&webp_p=1">1</a>
                                        </li>
                                        <?php if ($webp_start_page > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif;
                                    endif;
                                    
                                    
                                    for ($i = $webp_start_page; $i <= $webp_end_page; $i++): ?>
                                        <li class="page-item <?= $i == $webp_current_page ? 'active' : '' ?>">
                                            <?php if ($i == $webp_current_page): ?>
                                                <span class="page-link"><?= $i ?></span>
                                            <?php else: ?>
                                                <a class="page-link" href="admin.php?<?= $webp_base_query ?>&webp_p=<?= $i ?>"><?= $i ?></a>
                                            <?php endif; ?>
                                        </li>
                                    <?php endfor;
                                    
                                    
                                    if ($webp_end_page < $webp_total_pages): ?>
                                        <?php if ($webp_end_page < $webp_total_pages - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $webp_base_query ?>&webp_p=<?= $webp_total_pages ?>"><?= $webp_total_pages ?></a>
                                        </li>
                                    <?php endif;
                                    
                                    
                                    if ($webp_current_page < $webp_total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="admin.php?<?= $webp_base_query ?>&webp_p=<?= $webp_current_page + 1 ?>" aria-label="Próxima">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-label="Próxima">
                                                <span aria-hidden="true">&raquo;</span>
                                            </span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            
                            <!-- WebP Results info -->
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    Mostrando <?= min(($webp_current_page - 1) * 20 + 1, $webp_total_images ?? 0) ?> a <?= min($webp_current_page * 20, $webp_total_images ?? 0) ?> de <?= $webp_total_images ?? 0 ?> imagens WebP
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllJpeg = document.getElementById('select_all_jpeg');
    const jpegCheckboxes = document.querySelectorAll('.jpeg_checkbox');
    const selectAllWebp = document.getElementById('select_all_webp');
    const webpCheckboxes = document.querySelectorAll('.webp_checkbox');

    if(selectAllJpeg) {
        selectAllJpeg.addEventListener('change', function(e) {
            jpegCheckboxes.forEach(checkbox => checkbox.checked = e.target.checked);
        });
    }

    if(selectAllWebp) {
        selectAllWebp.addEventListener('change', function(e) {
            webpCheckboxes.forEach(checkbox => checkbox.checked = e.target.checked);
        });
    }
});
</script>