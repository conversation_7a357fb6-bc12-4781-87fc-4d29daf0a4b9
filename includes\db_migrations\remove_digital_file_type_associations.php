<?php

function migrate_remove_digital_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            return true;
        }

        
        $pdo->beginTransaction();

        
        $pdo->exec("DROP TABLE IF EXISTS digital_file_type_associations;");

        
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_file_type_associations_file_id;");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_file_type_associations_type_id;");

        
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'remove_digital_file_type_associations']);

        
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
