<?php
if (!isset($attribute_id) || !filter_var($attribute_id, FILTER_VALIDATE_INT)) {
    echo '<div class="alert alert-danger">ID do Atributo inválido ou em falta.</div>';
    echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$attribute = db_query("SELECT * FROM attributes WHERE id = :id", [':id' => $attribute_id], true);
if (!$attribute) {
    echo '<div class="alert alert-danger">Atributo pai não encontrado (ID: ' . $attribute_id . ').</div>';
    echo '<a href="admin.php?section=attributes&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Atributos</a>';
    return;
}

$action_type = $_GET['action_type'] ?? 'add';
$value_id = isset($_GET['value_id']) ? (int)$_GET['value_id'] : null;
$is_edit = ($action_type === 'edit' && $value_id);

$value_data = [
    'id' => '',
    'value_pt' => '',
    'price_modifier' => '0.00'
];

if ($is_edit) {
    $existing_value = db_query("SELECT * FROM attribute_values WHERE id = :id AND attribute_id = :aid", 
        [':id' => $value_id, ':aid' => $attribute_id], true);
    
    if (!$existing_value) {
        echo '<div class="alert alert-danger">Valor do atributo não encontrado.</div>';
        echo '<a href="admin.php?section=attributes&action=values&attribute_id=' . $attribute_id . '&' . get_session_id_param() . '" class="btn btn-secondary mt-2">Voltar aos Valores</a>';
        return;
    }
    
    $value_data = $existing_value;
}

if (isset($admin_view_data['form_data'])) {
    $value_data = array_merge($value_data, $admin_view_data['form_data']);
}
?>

<h1><?= $is_edit ? 'Editar' : 'Adicionar' ?> Valor para: <?= sanitize_input($attribute['name_pt']) ?></h1>
<hr>

<div class="mb-3">
    <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Voltar aos Valores
    </a>
</div>

<?php display_flash_messages(); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= $is_edit ? 'Editar' : 'Novo' ?> Valor do Atributo</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" id="attribute-value-form">
                    <?= csrf_input_field() ?>
                    <input type="hidden" name="attribute_id" value="<?= $attribute_id ?>">
                    <input type="hidden" name="action_type" value="<?= $action_type ?>">
                    <?php if ($is_edit): ?>
                        <input type="hidden" name="value_id" value="<?= $value_id ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="value_pt" class="form-label">Valor (Português) <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="value_pt" name="value_pt" 
                               value="<?= sanitize_input($value_data['value_pt']) ?>" 
                               placeholder="Ex: Vermelho, Grande, 100ml" required>
                        <div class="form-text">Nome do valor que será exibido aos clientes.</div>
                    </div>

                    <div class="mb-3">
                        <label for="price_modifier" class="form-label">Modificador de Preço</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= get_setting('currency_symbol', '€') ?></span>
                            <input type="number" step="0.01" class="form-control" id="price_modifier" 
                                   name="price_modifier" value="<?= sanitize_input($value_data['price_modifier']) ?>" 
                                   placeholder="0.00">
                        </div>
                        <div class="form-text">
                            <strong>Exemplos:</strong><br>
                            • <code>0.00</code> - Sem alteração no preço<br>
                            • <code>5.00</code> - Adiciona €5.00 ao preço base<br>
                            • <code>-2.50</code> - Reduz €2.50 do preço base
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&<?= get_session_id_param() ?>" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> <?= $is_edit ? 'Atualizar' : 'Criar' ?> Valor
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Informações</h6>
            </div>
            <div class="card-body">
                <p><strong>Atributo:</strong> <?= sanitize_input($attribute['name_pt']) ?></p>
                
                <?php if ($is_edit): ?>
                    <p><strong>ID do Valor:</strong> <?= $value_data['id'] ?></p>
                <?php endif; ?>
                
                <hr>
                
                <h6>Dicas:</h6>
                <ul class="small">
                    <li>Use nomes descritivos e claros</li>
                    <li>O modificador de preço é opcional</li>
                    <li>Valores positivos aumentam o preço</li>
                    <li>Valores negativos reduzem o preço</li>
                    <li>Use 0.00 para opções sem custo adicional</li>
                </ul>
            </div>
        </div>
        
        <?php if ($is_edit): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0 text-danger"><i class="bi bi-exclamation-triangle"></i> Zona de Perigo</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">Remover este valor irá afetá-lo em todos os produtos que o utilizam.</p>
                <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute_id ?>&delete_id=<?= $value_id ?>&<?= get_session_id_param() ?>" 
                   class="btn btn-outline-danger btn-sm w-100"
                   onclick="return confirm('Tem a certeza que quer remover este valor? Esta ação não pode ser desfeita e irá afetar todos os produtos que usam este valor.');">
                    <i class="bi bi-trash"></i> Remover Valor
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('attribute-value-form');
    const valueInput = document.getElementById('value_pt');
    const priceInput = document.getElementById('price_modifier');
    
    // Auto-focus on value input for new values
    <?php if (!$is_edit): ?>
    valueInput.focus();
    <?php endif; ?>
    
    // Form validation
    form.addEventListener('submit', function(e) {
        const value = valueInput.value.trim();
        
        if (value.length === 0) {
            e.preventDefault();
            alert('Por favor, insira um valor para o atributo.');
            valueInput.focus();
            return false;
        }
        
        if (value.length > 255) {
            e.preventDefault();
            alert('O valor não pode ter mais de 255 caracteres.');
            valueInput.focus();
            return false;
        }
    });
    
    // Price input formatting
    priceInput.addEventListener('blur', function() {
        const value = parseFloat(this.value) || 0;
        this.value = value.toFixed(2);
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+S or Cmd+S to save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            form.submit();
        }
        
        // Escape to cancel
        if (e.key === 'Escape') {
            const cancelBtn = document.querySelector('a.btn-secondary');
            if (cancelBtn && confirm('Tem a certeza que quer cancelar? As alterações não guardadas serão perdidas.')) {
                window.location.href = cancelBtn.href;
            }
        }
    });
});
</script>