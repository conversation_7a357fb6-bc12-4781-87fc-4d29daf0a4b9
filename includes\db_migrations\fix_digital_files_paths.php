<?php

function migrate_fix_digital_files_paths()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        
        
        $pdo->exec("UPDATE digital_files SET file_path = '../digital_products/' || substr(file_path, instr(file_path, 'digital_')) WHERE file_path LIKE 'C:%'");

        
        $pdo->exec("UPDATE digital_files SET file_path = REPLACE(file_path, '../digital_products/digital_products/', '../digital_products/') WHERE file_path LIKE '../digital_products/digital_products/%'");

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
