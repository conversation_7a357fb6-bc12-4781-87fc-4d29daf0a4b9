<?php

function add_user_ip_to_sessions_migration($pdo) {
    try {
        
        $stmt = $pdo->query("PRAGMA table_info(sessions)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $user_ip_exists = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'user_ip') {
                $user_ip_exists = true;
                break;
            }
        }
        
        if (!$user_ip_exists) {
            $pdo->exec("ALTER TABLE sessions ADD COLUMN user_ip TEXT");
        } else {
        }
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}