<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$is_editing = $action === 'edit' && $item_id > 0;

$license = [];
$selected_digital_products = [];
$form_data = [];

if ($is_editing) {
    
    $license = get_license_by_id($item_id);

    if (!$license) {
        add_flash_message('Licença não encontrada.', 'danger');
        
        header('Location: admin.php?section=licenses&' . get_session_id_param());
        exit;
    }

    
    $license['customer_name'] = get_decrypted_license_name($license);
    $license['customer_email'] = get_decrypted_license_email($license);
    
    $digital_products = get_license_digital_products($item_id);
    $selected_digital_products = array_column($digital_products, 'id');

    
    $form_data = $license;
} else {
    
    $form_data = [
        'license_code' => '',
        'customer_name' => '',
        'customer_email' => '',
        'status' => 'active',
        'expiry_date' => date('Y-m-d', strtotime('+' . get_setting('digital_download_expiry_days', 5) . ' days')),
        'download_limit' => get_setting('digital_download_limit', 3)
    ];
}

$all_digital_products = db_query(
    "SELECT dp.id, p.name_pt, p.slug, df.file_path, df.original_filename, df.display_name AS df_display_name
     FROM digital_products dp
     JOIN products p ON dp.product_id = p.id
     LEFT JOIN digital_files df ON dp.digital_file_id = df.id
     WHERE p.is_active = 1 AND p.product_type = 'digital'
     ORDER BY p.name_pt ASC",
    [],
    false, true
);
$all_digital_products = is_array($all_digital_products) ? $all_digital_products : [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    $license_code = sanitize_input($_POST['license_code'] ?? '');
    $customer_name = sanitize_input($_POST['customer_name'] ?? '');
    $customer_email = sanitize_input($_POST['customer_email'] ?? '');
    $status = sanitize_input($_POST['status'] ?? '');
    $expiry_date = sanitize_input($_POST['expiry_date'] ?? '');
    $download_limit = filter_input(INPUT_POST, 'download_limit', FILTER_VALIDATE_INT);
    $digital_product_ids = $_POST['digital_product_ids'] ?? [];

    
    $errors = [];

    if (empty($customer_name)) {
        $errors[] = 'O nome do cliente é obrigatório.';
    }

    if (empty($customer_email)) {
        $errors[] = 'O email do cliente é obrigatório.';
    } elseif (!filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'O email do cliente é inválido.';
    }

    if (!in_array($status, ['active', 'waiting_payment', 'disabled', 'canceled'])) {
        $errors[] = 'Status inválido.';
    }

    if (empty($expiry_date)) {
        $errors[] = 'A data de expiração é obrigatória.';
    }

    if ($download_limit === false || $download_limit < 1) {
        $errors[] = 'O limite de downloads deve ser um número inteiro maior que zero.';
    }

    if (empty($digital_product_ids)) {
        $errors[] = 'Selecione pelo menos um produto digital.';
    }

    
    if (!empty($license_code) && (!$is_editing || $license_code !== $license['license_code'])) {
        $existing_license = get_license_by_code($license_code);
        if ($existing_license) {
            $errors[] = 'Este código de licença já está em uso.';
        }
    }

    if (empty($errors)) {
        
        $license_data = [
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'status' => $status,
            'expiry_date' => $expiry_date . ' 23:59:59', 
            'download_limit' => $download_limit,
            'digital_product_ids' => $digital_product_ids
        ];

        if (!empty($license_code)) {
            $license_data['license_code'] = $license_code;
        }

        if ($is_editing) {
            
            if (update_license($item_id, $license_data)) {
                add_flash_message('Licença atualizada com sucesso.', 'success');
                
                header('Location: admin.php?section=licenses&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao atualizar licença.', 'danger');
            }
        } else {
            
            $license_id = create_license($license_data);
            if ($license_id) {
                add_flash_message('Licença criada com sucesso.', 'success');
                
                header('Location: admin.php?section=licenses&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao criar licença.', 'danger');
            }
        }
    } else {
        
        foreach ($errors as $error) {
            add_flash_message($error, 'danger');
        }

        
        $form_data = [
            'license_code' => $license_code,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'status' => $status,
            'expiry_date' => $expiry_date,
            'download_limit' => $download_limit
        ];
        $selected_digital_products = $digital_product_ids;
    }
}

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $is_editing ? 'Editar Licença' : 'Nova Licença' ?></h1>
        <a href="<?= $is_editing ? 'admin.php?section=licenses&action=detail&id=' . $item_id : 'admin.php?section=licenses' ?>&<?= get_session_id_param() ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" action="admin.php?section=licenses&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>">
                <?= csrf_input_field() ?>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="license_code" class="form-label">Código de Licença</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="license_code" name="license_code"
                                   value="<?= sanitize_input($form_data['license_code']) ?>"
                                   <?= $is_editing ? 'readonly' : '' ?>>
                            <?php if (!$is_editing): ?>
                                <button type="button" class="btn btn-primary" id="generate_license_btn">
                                    <i class="bi bi-key-fill"></i> Gerar Código
                                </button>
                            <?php endif; ?>
                        </div>
                        <div class="form-text">
                            <?= $is_editing ? 'O código de licença não pode ser alterado.' : 'Deixe em branco para gerar automaticamente ou clique no botão para gerar agora.' ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" <?= $form_data['status'] === 'active' ? 'selected' : '' ?>>Ativo</option>
                            <option value="waiting_payment" <?= $form_data['status'] === 'waiting_payment' ? 'selected' : '' ?>>Aguardando Pagamento</option>
                            <option value="disabled" <?= $form_data['status'] === 'disabled' ? 'selected' : '' ?>>Desativado</option>
                            <option value="canceled" <?= $form_data['status'] === 'canceled' ? 'selected' : '' ?>>Cancelado</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="customer_name" class="form-label">Nome do Cliente *</label>
                        <input type="text" class="form-control" id="customer_name" name="customer_name"
                               value="<?= sanitize_input($form_data['customer_name']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="customer_email" class="form-label">Email do Cliente *</label>
                        <input type="email" class="form-control" id="customer_email" name="customer_email"
                               value="<?= sanitize_input($form_data['customer_email']) ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="expiry_date" class="form-label">Data de Expiração *</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                               value="<?= substr($form_data['expiry_date'], 0, 10) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="download_limit" class="form-label">Limite de Downloads *</label>
                        <input type="number" class="form-control" id="download_limit" name="download_limit"
                               value="<?= (int)$form_data['download_limit'] ?>" min="1" required>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="form-label">Produtos Digitais *</label>
                    <?php if (empty($all_digital_products)): ?>
                        <div class="alert alert-warning" role="alert">
                            Nenhum produto digital disponível. <a href="admin.php?section=products&action=new&<?= get_session_id_param() ?>">Criar um produto digital</a>.
                        </div>
                    <?php else: ?>
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <?php foreach ($all_digital_products as $product): ?>
                                        <div class="list-group-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="digital_product_ids[]"
                                                       value="<?= (int)$product['id'] ?>"
                                                       id="product_<?= (int)$product['id'] ?>"
                                                       <?= in_array($product['id'], $selected_digital_products) ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="product_<?= (int)$product['id'] ?>">
                                                    <strong><?= sanitize_input($product['name_pt']) ?></strong>
                                                    <small class="d-block text-muted">
                                                        Arquivo: <?= !empty($product['df_display_name']) ? sanitize_input($product['df_display_name']) : (!empty($product['original_filename']) ? sanitize_input($product['original_filename']) : (!empty($product['file_path']) ? basename(sanitize_input($product['file_path'])) : 'N/A')) ?>
                                                    </small>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="<?= $is_editing ? 'admin.php?section=licenses&action=detail&id=' . $item_id : 'admin.php?section=licenses' ?>&<?= get_session_id_param() ?>" class="btn btn-secondary me-2">
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <?= $is_editing ? 'Atualizar' : 'Criar' ?> Licença
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate license code button
    const generateLicenseBtn = document.getElementById('generate_license_btn');
    if (generateLicenseBtn) {
        generateLicenseBtn.addEventListener('click', function() {
            // Make AJAX request to generate a license code
            fetch('admin.php?section=licenses&action=generate_code&is_ajax=1&<?= get_session_id_param() ?>', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'  // Add this header to properly identify AJAX requests
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.license_code) {
                        document.getElementById('license_code').value = data.license_code;
                    } else {
                        alert('Erro ao gerar código de licença: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    alert('Erro ao gerar código de licença. Verifique o console para mais detalhes.');
                });
        });
    }
});
</script>
