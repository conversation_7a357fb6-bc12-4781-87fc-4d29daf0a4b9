Options -Indexes

# Define the custom error page for HTTP 404 errors
ErrorDocument 404 /templates/frontend/404_redirect.php

<Files "admin.php">
  AuthType Basic
  AuthName "Área Restrita"
  AuthUserFile /home/<USER>/.htpasswds/.htpasswd-admin
  Require valid-user
</Files>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php82” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php82___lsphp .php .php8 .phtml
</IfModule>
# php -- <PERSON><PERSON> cPanel-generated handler, do not edit
