<!doctype html>
<html lang="pt" data-bs-theme="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= sanitize_input($page_title ?? 'Admin Area') ?></title>

    <!-- CSRF Token for JavaScript -->
    <script>
        <?php if ($is_admin_logged_in && function_exists('session_id') && session_id()): ?>
        window.eshopSessionId = '<?= session_id() ?>';
        <?php endif; ?>
        <?php if (!empty($csrf_token_for_js)): ?>
        window.csrfToken = '<?= $csrf_token_for_js ?>';
        <?php endif; ?>
    </script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Flag Icons CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.3.2/css/flag-icons.min.css" />

    <!-- Summernote CSS (for WYSIWYG) -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.css" rel="stylesheet">

    <!-- jQuery (Load in head to ensure availability for all scripts) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

    <!-- Optional: Custom Admin CSS -->
    <?php  ?>
    <style>
        /* Admin Layout System - Bootstrap 5 Compatible */
        :root {
            --sidebar-width: 280px;
            --sidebar-bg: var(--bs-dark-bg-subtle);
            --sidebar-link-color: var(--bs-light);
            --sidebar-active-bg: var(--bs-primary);
            --sidebar-hover-bg: var(--bs-secondary);
            --submenu-bg: rgba(0, 0, 0, 0.1);
            --submenu-indent: 1.5rem;
        }

        html, body {
            height: 100%;
            overflow-x: hidden;
        }

        /* Main Layout Container */
        .admin-layout {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        /* Sidebar Styling */
        .admin-sidebar {
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            z-index: 1030;
            background-color: var(--sidebar-bg);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        /* Main Content Area */
        .admin-content {
            flex: 1;
            width: calc(100% - var(--sidebar-width));
            margin-left: var(--sidebar-width);
            transition: margin-left 0.3s ease, width 0.3s ease;
        }

        /* Sidebar Navigation */
        .admin-sidebar .nav-link {
            color: var(--sidebar-link-color);
            padding: 0.75rem 1.25rem;
            border-radius: 0;
            transition: background-color 0.2s ease;
        }

        .admin-sidebar .nav-link:hover {
            background-color: var(--sidebar-hover-bg);
        }

        .admin-sidebar .nav-link.active {
            background-color: var(--sidebar-active-bg);
            color: white;
        }

        .admin-sidebar .nav-link .bi {
            margin-right: 0.5rem;
            width: 1.25rem;
            text-align: center;
        }

        /* Sidebar Header */
        .admin-sidebar-header {
            padding: 1.5rem 1.25rem 1rem;
        }

        /* Accordion Menu Styles */
        .accordion-menu .menu-group-toggle {
            position: relative;
            cursor: pointer;
            font-weight: 500;
        }

        .accordion-menu .menu-group-toggle .toggle-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }

        .accordion-menu .menu-group-toggle.expanded .toggle-icon {
            transform: translateY(-50%) rotate(180deg);
        }

        .accordion-menu .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: var(--submenu-bg);
        }

        .accordion-menu .submenu.show {
            max-height: 500px; /* Large enough to contain all items */
        }

        .accordion-menu .submenu .nav-link {
            padding-left: var(--submenu-indent);
        }

        /* Active state for parent when child is active */
        .accordion-menu .menu-group-toggle.has-active-child {
            background-color: rgba(var(--bs-primary-rgb), 0.2);
        }

        /* Sidebar Footer */
        .admin-sidebar-footer {
            padding: 1rem 1.25rem;
            border-top: 1px solid var(--bs-border-color);
            margin-top: auto;
        }

        /* Responsive Adjustments */
        @media (max-width: 767.98px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-content {
                width: 100%;
                margin-left: 0;
            }

            /* Mobile Toggle Button */
            .sidebar-toggle {
                display: block;
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 1040;
            }
        }
    </style>
</head>
<body>

<div class="admin-layout">
    <?php if ($is_admin_logged_in): ?>
    <!-- Admin Sidebar -->
    <aside class="admin-sidebar d-flex flex-column">
        <div class="admin-sidebar-header">
            <a href="admin.php?<?= get_session_id_param() ?>" class="d-flex align-items-center text-white text-decoration-none">
                <i class="bi bi-shield-lock fs-4 me-2"></i>
                <span class="fs-4">Admin Area</span>
            </a>
        </div>
        <hr class="my-2">
        <ul class="nav nav-pills flex-column mb-auto px-0 accordion-menu">
            <!-- Dashboard (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=dashboard&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'dashboard') ? 'active' : '' ?>" aria-current="page" data-section="dashboard">
                    <i class="bi bi-speedometer2"></i> Dashboard
                </a>
            </li>

            <!-- Produtos Group -->
            <li class="nav-item menu-group">
                <a href="#" class="nav-link menu-group-toggle <?= (in_array($section, ['products', 'attributes', 'categories', 'coupons', 'custom_fields', 'custom_field_fonts'])) ? 'expanded' : '' ?>">
                    <i class="bi bi-box-seam"></i> Produtos
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
                <ul class="nav nav-pills flex-column submenu <?= (in_array($section, ['products', 'attributes', 'categories', 'coupons', 'custom_fields', 'custom_field_fonts'])) ? 'show' : '' ?>">
                    <li>
                        <a href="admin.php?section=products&action=list&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'products' && ($action === 'list' || $action === '')) ? 'active' : '' ?>" data-section="products">
                            <i class="bi bi-list-ul"></i> Lista
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=products&action=images&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'products' && $action === 'images') ? 'active' : '' ?>" data-section="products" data-action="images">
                            <i class="bi bi-images"></i> Gerir Imagens
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=attributes&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'attributes') ? 'active' : '' ?>" data-section="attributes">
                            <i class="bi bi-tags"></i> Atributos
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=categories&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'categories') ? 'active' : '' ?>" data-section="categories">
                            <i class="bi bi-bookmark"></i> Categorias
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=custom_fields&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'custom_fields') ? 'active' : '' ?>" data-section="custom_fields">
                            <i class="bi bi-input-cursor-text"></i> Campos Personalizados
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=custom_field_fonts&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'custom_field_fonts') ? 'active' : '' ?>" data-section="custom_field_fonts">
                            <i class="bi bi-type"></i> Fontes
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=coupons&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'coupons') ? 'active' : '' ?>" data-section="coupons">
                            <i class="bi bi-ticket-perforated"></i> Cupões
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Produtos Digitais Group -->
            <li class="nav-item menu-group">
                <a href="#" class="nav-link menu-group-toggle <?= (in_array($section, ['digital_files', 'digital_products', 'licenses', 'download_statistics'])) ? 'expanded' : '' ?>">
                    <i class="bi bi-file-earmark-zip"></i> Produtos Digitais
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
                <ul class="nav nav-pills flex-column submenu <?= (in_array($section, ['digital_files', 'digital_products', 'licenses', 'download_statistics'])) ? 'show' : '' ?>">
                    <li>
                        <a href="admin.php?section=digital_files&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'digital_files') ? 'active' : '' ?>" data-section="digital_files">
                            <i class="bi bi-file-earmark"></i> Arquivos Digitais
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=digital_products&action=list&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'digital_products') ? 'active' : '' ?>" data-section="digital_products">
                            <i class="bi bi-box-seam"></i> Produtos Digitais
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=licenses&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'licenses') ? 'active' : '' ?>" data-section="licenses">
                            <i class="bi bi-key"></i> Licenças
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=download_statistics&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'download_statistics') ? 'active' : '' ?>" data-section="download_statistics">
                            <i class="bi bi-graph-up"></i> Estatísticas de Downloads
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Encomendas Group -->
            <li class="nav-item menu-group">
                <a href="#" class="nav-link menu-group-toggle <?= (in_array($section, ['orders'])) ? 'expanded' : '' ?>">
                    <i class="bi bi-receipt"></i> Encomendas
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
                <ul class="nav nav-pills flex-column submenu <?= (in_array($section, ['orders'])) ? 'show' : '' ?>">
                    <li>
                        <a href="admin.php?section=orders&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'orders') ? 'active' : '' ?>" data-section="orders">
                            <i class="bi bi-list-ul"></i> Lista
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Páginas Group -->
            <li class="nav-item menu-group">
                <a href="#" class="nav-link menu-group-toggle <?= (in_array($section, ['pages', 'page_categories', 'page_placeholders', 'placeholder_links'])) ? 'expanded' : '' ?>">
                    <i class="bi bi-file-earmark-text"></i> Páginas
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
                <ul class="nav nav-pills flex-column submenu <?= (in_array($section, ['pages', 'page_categories', 'page_placeholders', 'placeholder_links'])) ? 'show' : '' ?>">
                    <li>
                        <a href="admin.php?section=pages&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'pages') ? 'active' : '' ?>" data-section="pages">
                            <i class="bi bi-list-ul"></i> Lista
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=page_categories&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'page_categories') ? 'active' : '' ?>" data-section="page_categories">
                            <i class="bi bi-folder2-open"></i> Categorias
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=page_placeholders&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'page_placeholders') ? 'active' : '' ?>" data-section="page_placeholders">
                            <i class="bi bi-layout-text-sidebar"></i> Placeholders
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=placeholder_links&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'placeholder_links') ? 'active' : '' ?>" data-section="placeholder_links">
                            <i class="bi bi-link-45deg"></i> Links
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Blog Group -->
            <li class="nav-item menu-group">
                <a href="#" class="nav-link menu-group-toggle <?= (in_array($section, ['blog_posts', 'blog_categories'])) ? 'expanded' : '' ?>">
                    <i class="bi bi-newspaper"></i> Blog
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
                <ul class="nav nav-pills flex-column submenu <?= (in_array($section, ['blog_posts', 'blog_categories'])) ? 'show' : '' ?>">
                    <li>
                        <a href="admin.php?section=blog_posts&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'blog_posts') ? 'active' : '' ?>" data-section="blog_posts">
                            <i class="bi bi-list-ul"></i> Posts
                        </a>
                    </li>
                    <li>
                        <a href="admin.php?section=blog_categories&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'blog_categories') ? 'active' : '' ?>" data-section="blog_categories">
                            <i class="bi bi-folder2-open"></i> Categorias
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Mensagens (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=messages&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'messages') ? 'active' : '' ?>" data-section="messages">
                    <i class="bi bi-envelope"></i> Mensagens
                </a>
            </li>

            <!-- Métodos de Pagamento (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=payment_methods&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'payment_methods') ? 'active' : '' ?>" data-section="payment_methods">
                    <i class="bi bi-credit-card"></i> Métodos de Pagamento
                </a>
            </li>

            <!-- Banners (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=banners&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'banners') ? 'active' : '' ?>" data-section="banners">
                    <i class="bi bi-images"></i> Banners
                </a>
            </li>

            <!-- SEO & Sitemaps (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=sitemaps&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'sitemaps') ? 'active' : '' ?>" data-section="sitemaps">
                    <i class="bi bi-diagram-3"></i> SEO & Sitemaps
                </a>
            </li>

            <!-- Configurações (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=settings&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'settings') ? 'active' : '' ?>" data-section="settings">
                    <i class="bi bi-gear"></i> Configurações
                </a>
            </li>

            <!-- Manutenção (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=maintenance&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'maintenance') ? 'active' : '' ?>" data-section="maintenance">
                    <i class="bi bi-tools"></i> Manutenção
                </a>
            </li>

            <!-- Sessões (standalone) -->
            <li class="nav-item">
                <a href="admin.php?section=sessions&<?= get_session_id_param() ?>" class="nav-link <?= ($section === 'sessions') ? 'active' : '' ?>" data-section="sessions">
                    <i class="bi bi-people"></i> Gerir Sessões
                </a>
            </li>
        </ul>
        <div class="admin-sidebar-footer mt-auto">
            <a href="admin.php?action=logout" class="nav-link text-white" data-no-ajax="true" onclick="window.location.href='admin.php?action=logout'; return false;">
                <i class="bi bi-box-arrow-right"></i> Sair
            </a>
        </div>
    </aside>

    <!-- Mobile Sidebar Toggle (only visible on small screens) -->
    <button class="btn btn-primary sidebar-toggle d-md-none" type="button" aria-label="Toggle Sidebar">
        <i class="bi bi-list"></i>
    </button>
    <?php endif; ?>

    <!-- Main Content Wrapper -->
    <div class="admin-content">
        <!-- Toast container for notifications -->
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;"></div>

        <!-- Content container will be added in admin.php -->
