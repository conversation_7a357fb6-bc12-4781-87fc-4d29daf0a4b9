document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    initAccordionMenu();
    initAjaxNavigation();

    function initAccordionMenu() {
        const menuToggles = document.querySelectorAll('.menu-group-toggle');

        menuToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                this.classList.toggle('expanded');
                const submenu = this.nextElementSibling;
                if (submenu && submenu.classList.contains('submenu')) {
                    submenu.classList.toggle('show');
                }
            });
        });

        highlightActiveMenuParent();
    }

    function highlightActiveMenuParent() {
        const activeLink = document.querySelector('.submenu .nav-link.active');
        if (activeLink) {
            const parentToggle = activeLink.closest('.menu-group')?.querySelector('.menu-group-toggle');
            if (parentToggle) {
                parentToggle.classList.add('has-active-child');
            }
        }
    }

    function initAjaxNavigation() {
        const navLinks = document.querySelectorAll('.admin-sidebar .nav-link:not(.menu-group-toggle)');

        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                
                if (this.getAttribute('data-no-ajax') === 'true') {
                    return; 
                }

                e.preventDefault();
                const url = this.getAttribute('href');
                if (!url || url === '#') return;

                const relativeUrl = url.includes('?') ? 'admin.php' + url.substring(url.indexOf('?')) : url;
                window.history.pushState({ url: url }, '', relativeUrl);
                loadContent(url);
            });
        });

        
        document.addEventListener('submit', function(e) {
            const form = e.target;
            
            if (form.hasAttribute('data-no-ajax') && form.getAttribute('data-no-ajax') === 'true') {
                
                return;
            }
        }, true); 

        window.addEventListener('popstate', function(e) {
            if (e.state && e.state.url) {
                loadContent(e.state.url);
            } else {
                const currentUrl = window.AdminUrlHelper
                    ? window.AdminUrlHelper.getAdminBaseUrl() + 'admin.php' + window.location.search
                    : window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'admin.php' + window.location.search;
                loadContent(currentUrl);
            }
        });
    }

    
    const retryAttempts = new Map();
    const MAX_RETRIES = 1; 

    function loadContent(url, retryCount = 0) {

        const contentArea = document.querySelector('.admin-content main');
        if (contentArea) {
            contentArea.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div><p class="mt-3">A carregar conteúdo...</p></div>';
        }

        let fullUrl;
        if (window.AdminUrlHelper) {
            fullUrl = window.AdminUrlHelper.getAbsoluteAdminUrl(url);
        } else {
            if (url.startsWith('admin.php') || url.startsWith('/admin.php')) {
                const currentPath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
                fullUrl = window.location.origin + currentPath + url;
            } else {
                fullUrl = url;
            }
        }

        const ajaxUrl = new URL(fullUrl);
        ajaxUrl.searchParams.append('ajax', '1');

        
        const sessionId = getSessionId();
        if (sessionId && !ajaxUrl.searchParams.has('sid')) {
            ajaxUrl.searchParams.append('sid', sessionId);
        }

        fetch(ajaxUrl.toString(), {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-Session-UUID': sessionId || '' 
            },
            credentials: 'same-origin' 
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            
            retryAttempts.delete(url);

            if (data.redirect) {
                window.location.href = data.redirect;
                return;
            }

            if (contentArea && data.content) {
                contentArea.innerHTML = data.content;
                initializeContentScripts(data);

                if (data.content.trim().length === 0 ||
                    (data.section === 'pages' && !data.content.includes('Lista de Páginas')) ||
                    (data.section === 'page_categories' && !data.content.includes('Lista de Categorias')) ||
                    (data.section === 'page_placeholders' && !data.content.includes('Lista de Placeholders')) ||
                    (data.section === 'digital_products' && data.action === 'list' && !data.content.includes('Lista de Produtos Digitais'))) {
                    window.location.reload();
                    return;
                }
            }

            updateActiveMenuItem(data.section, data.action);

            if (data.flash_messages && data.flash_messages.length > 0) {
                data.flash_messages.forEach(message => {
                    if (window.AdminUtils) {
                        window.AdminUtils.showNotification(message.message, message.type);
                    }
                });
            }

            if (data.page_title) {
                document.title = data.page_title;
            }

            
            if (data.section === 'blog_posts') {
            }

            
            if (data.section === 'sitemaps') {

                
                const deleteButtons = document.querySelectorAll('.delete-sitemap-btn');
                deleteButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const sitemapId = this.getAttribute('data-id');
                        if (confirm('Tem a certeza que deseja eliminar esta configuração de sitemap/XML?')) {
                            window.location.href = `admin.php?section=sitemaps&action=delete&id=${sitemapId}&${getSessionId() ? 'sid=' + getSessionId() : ''}`;
                        }
                    });
                });

                
                const newSitemapBtn = document.querySelector('a[href*="section=sitemaps&action=new"]');
                if (newSitemapBtn) {
                    newSitemapBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const url = this.getAttribute('href');
                        window.location.href = url;
                    });
                }
            }

            
            

        })
        .catch(error => {

            
            if (retryCount < MAX_RETRIES) {

                
                setTimeout(() => {
                    loadContent(url, retryCount + 1);
                }, 500);
                return;
            }

            
            if (contentArea) {
                contentArea.innerHTML = `
                    <div class="alert alert-danger m-3" role="alert">
                        <h4 class="alert-heading">Erro ao carregar conteúdo</h4>
                        <p>Ocorreu um erro ao carregar o conteúdo. Por favor, tente novamente ou recarregue a página.</p>
                        <hr>
                        <p>URL: ${ajaxUrl.toString()}</p>
                        <p class="mb-0">Detalhes técnicos: ${error.message}</p>
                        <div class="mt-3">
                            <button class="btn btn-outline-secondary btn-sm" onclick="loadContent('${url}')">
                                <i class="bi bi-arrow-clockwise"></i> Tentar Novamente
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="window.location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> Recarregar Página
                            </button>
                            <a href="#" onclick="window.location.href = window.AdminUrlHelper ? window.AdminUrlHelper.getAbsoluteAdminUrl('admin.php') : 'admin.php'; return false;" class="btn btn-primary btn-sm ms-2">
                                <i class="bi bi-house"></i> Voltar ao Dashboard
                            </a>
                        </div>
                    </div>
                `;

                if (window.AdminUtils) {
                    window.AdminUtils.showNotification('Erro ao carregar conteúdo. Verifique a conexão e tente novamente.', 'danger');
                }
            }
        });
    }

    function initDashboardWithRetryViaAjax(retryCount = 0) {
        const maxRetries = 5; 
        const delays = [500, 1000, 2000, 3000, 4000]; 

        if (typeof Chart !== 'undefined') {
            if (typeof window.initDashboard === 'function') {
                window.initDashboard();
            } else {
                console.warn('initDashboard function not found after Chart.js is loaded (AJAX navigation).');
            }
            return;
        }

        if (retryCount >= maxRetries) {
            
            const contentArea = document.querySelector('.admin-content main');
            if (contentArea) {
                contentArea.innerHTML = '<div class="alert alert-warning m-3">Os gráficos do dashboard não puderam ser carregados. Verifique sua conexão ou tente recarregar a página.</div>';
            }
            return;
        }
        setTimeout(() => initDashboardWithRetryViaAjax(retryCount + 1), delays[retryCount] || 4000);
    }

    
    function getSessionId() {
        
        if (window.eshopSessionId) {
            return window.eshopSessionId;
        }

        
        const urlParams = new URLSearchParams(window.location.search);
        const sidParam = urlParams.get('sid');
        if (sidParam) {
            return sidParam;
        }

        return null;
    }

    function updateActiveMenuItem(section, action) {
        document.querySelectorAll('.admin-sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        let activeLink = null;
        
        
        if (action) {
            activeLink = document.querySelector(`.admin-sidebar .nav-link[data-section="${section}"][data-action="${action}"]`);
        }
        
        
        if (!activeLink) {
            activeLink = document.querySelector(`.admin-sidebar .nav-link[data-section="${section}"]`);
        }
        
        if (activeLink) {
            activeLink.classList.add('active');

            const parentGroup = activeLink.closest('.menu-group');
            if (parentGroup) {
                const parentToggle = parentGroup.querySelector('.menu-group-toggle');
                const submenu = parentGroup.querySelector('.submenu');

                if (parentToggle && submenu) {
                    parentToggle.classList.add('expanded', 'has-active-child');
                    submenu.classList.add('show');
                }
            }
        }
    }

    function initializeContentScripts(data = {}) {
        if (window.AdminUtils) {
            setTimeout(() => {
                window.AdminUtils.initCollapsibleCards();
                window.AdminUtils.initAjaxForms();
            }, 100);
        }

        
        setTimeout(() => {
            if (window.initPageSpecificFeatures) {
                try {
                    window.initPageSpecificFeatures();
                } catch (error) {
                }
            }

            
            if (data.section === 'orders' && window.initOrderDeleteButtons) {
                try {
                    
                    
                    window.orderDeleteButtonsInitialized = false;

                    
                    setTimeout(() => {
                        if (document.querySelector('.delete-order-btn') && !window.orderDeleteButtonsInitialized) {
                            window.initOrderDeleteButtons();
                        }
                    }, 300);
                } catch (error) {
                }
            }
        }, 100);

        
        if (typeof window.initSummernoteEditors === 'function') {
            window.initSummernoteEditors();
        }

        
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn !== 'undefined') {
            if (typeof jQuery.fn.DataTable !== 'undefined') {
                try {
                    jQuery('.datatable').DataTable({
                        "language": {
                            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese.json"
                        }
                    });
                } catch (error) {
                    console.warn('Error initializing DataTable:', error);
                }
            }
        } else {
            console.warn('jQuery not available for DataTables initialization in AJAX loaded content.');
        } 
        if (data.section === 'settings' && typeof initVatRateManagement === 'function') {
            try {
                
                window.initVatRateManagement();
            } catch (error) {
            }
        } else if (data.section !== 'settings') {
        } else if (typeof initVatRateManagement !== 'function') {
        }
       

       
       if (data.section === 'maintenance' && typeof initMaintenanceFeatures === 'function') {
           try {
               
               window.initMaintenanceFeatures();
           } catch (error) {
           }
       }
       
       
       if (data.section === 'sessions' && typeof initSessionsManagement === 'function') {
           try {
               window.initSessionsManagement();
           } catch (error) {
           }
       }
       

       
       if (data.section === 'dashboard') {
           try {
               
               if (typeof window.initDashboardButtons === 'function') {
                   window.initDashboardButtons();
               } else {
                   
                   document.querySelectorAll('.widget-collapse-icon').forEach(button => {
                       button.onclick = function(e) {
                           e.preventDefault(); e.stopPropagation();
                           const widget = this.closest('.dashboard-widget'); if (!widget) return false;
                           const body = widget.querySelector('.widget-body'); if (!body) return false;
                           if (body.style.display === 'none') {
                               body.style.display = 'block';
                               const icon = this.querySelector('i'); if (icon) icon.className = 'bi bi-chevron-up';
                           } else {
                               body.style.display = 'none';
                               const icon = this.querySelector('i'); if (icon) icon.className = 'bi bi-chevron-down';
                           }
                           return false;
                       };
                   });
                   document.querySelectorAll('.refresh-widget').forEach(button => {
                       button.onclick = function(e) {
                           e.preventDefault(); e.stopPropagation();
                           const widgetId = this.getAttribute('data-widget');
                           const widget = document.getElementById(widgetId);
                           if (widget) {
                               const widgetBody = widget.querySelector('.widget-body');
                               if (widgetBody) {
                                   widgetBody.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div></div>';
                                   setTimeout(() => { window.location.reload(); }, 1000);
                               }
                           }
                           return false;
                       };
                   });
               }

               
               initDashboardWithRetryViaAjax();

           } catch (error) {
           }
       }
       

   }

   
   window.loadContent = loadContent;
});
