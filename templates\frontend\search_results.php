<?php

$page_title = 'Resultados da Pesquisa para "' . htmlspecialchars($search_query) . '"';

$product_variations = [];
$attribute_value_modifiers = [];

$product_ids = [];
foreach ($search_results as $result) {
    if ($result['type'] === 'product' && isset($result['id'])) {
        $product_ids[] = $result['id'];
    }
}

if (!empty($product_ids)) {
    
    $product_ids_str = implode(',', array_map('intval', array_unique($product_ids)));

    
    $variations_raw = db_query(
        "SELECT pv.id as variation_id, pv.product_id, pv.sku, pv.stock, pv.is_active as variation_is_active,
                vv.value_id, av.value_pt, av.price_modifier as value_price_modifier,
                a.id as attribute_id, a.name_pt as attribute_name
         FROM product_variations pv
         JOIN variation_values vv ON pv.id = vv.variation_id
         JOIN attribute_values av ON vv.value_id = av.id
         JOIN attributes a ON av.attribute_id = a.id
         JOIN products p ON pv.product_id = p.id
         WHERE pv.product_id IN ($product_ids_str) AND pv.is_active = 1 AND p.is_active = 1
         ORDER BY pv.product_id, pv.id, a.name_pt, av.value_pt",
        [], false, true);

    
    if ($variations_raw) {
        foreach ($variations_raw as $row) {
            $product_id = $row['product_id'];

            
            if (!isset($product_variations[$product_id])) {
                $product_variations[$product_id] = [];
            }

            
            if (!isset($product_variations[$product_id][$row['variation_id']])) {
                $product_variations[$product_id][$row['variation_id']] = [
                    'sku' => $row['sku'],
                    'stock' => $row['stock'],
                    'options' => []
                ];
            }

            
            $product_variations[$product_id][$row['variation_id']]['options'][$row['attribute_id']] = $row['value_id'];

            
            if (!isset($attribute_value_modifiers[$row['value_id']])) {
                $attribute_value_modifiers[$row['value_id']] = (float)$row['value_price_modifier'];
            }
        }
    }
}

?>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6 text-white">
        Resultados da Pesquisa para: <span class="text-primary"><?= htmlspecialchars($search_query) ?></span>
    </h1>

    <?php if (!empty($search_query)): ?>
        <div class="mb-4 text-gray-400">
            <?php if ($total_results > 0): ?>
                Encontrados <?= number_format($total_results) ?> resultado<?= $total_results !== 1 ? 's' : '' ?>
                <?php if ($total_pages > 1): ?>
                    (página <?= $current_page ?> de <?= $total_pages ?>)
                <?php endif; ?>
            <?php else: ?>
                Nenhum resultado encontrado
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($search_results)) : ?>
        <!-- Display products in a grid layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            <?php
            
            $product_count = 0;
            $displayed_product_ids = []; 
            foreach ($search_results as $result) :
                if ($result['type'] === 'product') :
                    
                    
                    
                    

                    if (in_array($result['id'], $displayed_product_ids)) {
                        continue; 
                    }
                    $displayed_product_ids[] = $result['id']; 

                    $product_count++;
                    
                    
                    $product = [
                        'id' => $result['id'],
                        'name_pt' => $result['name'],
                        'slug' => $result['slug'] ?? '',
                        'base_price' => $result['price'],
                        'display_image_url' => $result['image_url'] ?? null
                    ];
                    
                    include __DIR__ . '/partials/product_card.php';
                endif;
            endforeach;
            ?>
        </div>

        <!-- Display pages in a list layout -->
        <?php
        $page_results = array_filter($search_results, function($result) {
            return $result['type'] === 'page';
        });

        if (!empty($page_results)) :
        ?>
        <h2 class="text-2xl font-bold mb-4 text-white">Páginas Relacionadas</h2>
        <div class="space-y-4 mb-8">
            <?php foreach ($page_results as $result) : ?>
                <div class="bg-gray-800 rounded-lg shadow-md p-4 flex items-start space-x-4">
                    <div class="flex-shrink-0 w-12 h-12 bg-gray-700 rounded flex items-center justify-center">
                        <i class="ri-file-text-line text-2xl text-gray-400"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-lg font-semibold text-white">
                            <a href="<?= $result['url'] ?>" class="hover:text-primary transition"><?= htmlspecialchars($result['name']) ?></a>
                        </h3>
                        <?php if (!empty($result['description'])) : ?>
                            <p class="text-gray-400 text-sm line-clamp-2">
                                <?= htmlspecialchars(strip_tags($result['description'])) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Pagination Controls -->
        <?php if ($total_pages > 1): ?>
        <div id="pagination-container" class="mt-8 sm:mt-10 md:mt-12 flex justify-center">
            <nav class="inline-flex flex-wrap justify-center rounded-md shadow-sm bg-gray-800" aria-label="Pagination">
                <?php
                // Build base URL for pagination, preserving search query
                $query_params = ['page' => 'search', 'q' => $search_query];
                $base_url = 'index.php?' . http_build_query($query_params) . '&';

                // Previous page button
                $prev_page = $current_page - 1;
                $prev_link_class = 'px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-l-button ';
                $prev_link_href = $base_url . 'p=' . $prev_page;
                if ($current_page <= 1) {
                    $prev_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
                    $prev_tag = 'span';
                    $prev_link_href = '#';
                } else {
                    $prev_link_class .= 'text-gray-300 hover:bg-gray-700';
                    $prev_tag = 'a';
                }
                ?>
                <<?= $prev_tag ?> href="<?= $prev_link_href ?>" class="<?= $prev_link_class ?>" <?= ($prev_tag === 'a' ? 'aria-label="Previous"' : '') ?>>
                    <i class="ri-arrow-left-s-line"></i>
                </<?= $prev_tag ?>>

                <?php
                // Calculate visible page range
                $max_visible_pages = 5;
                $half_max = floor($max_visible_pages / 2);

                $start_page = max(1, $current_page - $half_max);
                $end_page = min($total_pages, $start_page + $max_visible_pages - 1);

                // Adjust start_page if we don't have enough pages at the end
                if ($end_page - $start_page + 1 < $max_visible_pages) {
                    $start_page = max(1, $end_page - $max_visible_pages + 1);
                }

                // Show first page and ellipsis if needed
                if ($start_page > 1) {
                    $first_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-300 hover:bg-gray-700';
                    $first_link_href = $base_url . 'p=1';
                ?>
                    <a href="<?= $first_link_href ?>" class="<?= $first_link_class ?>">1</a>

                    <?php if ($start_page > 2): ?>
                    <span class="px-1 sm:px-2 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-500">...</span>
                    <?php endif; ?>
                <?php
                }

                // Show page numbers
                for ($i = $start_page; $i <= $end_page; $i++):
                    $page_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium ';
                    $page_link_href = $base_url . 'p=' . $i;
                    $page_tag = 'a';
                    $aria_current = '';

                    if ($i == $current_page) {
                        $page_link_class .= 'text-white bg-primary z-10 cursor-default';
                        $page_tag = 'span';
                        $aria_current = 'aria-current="page"';
                        $page_link_href = '#';
                    } else {
                        $page_link_class .= 'text-gray-300 hover:bg-gray-700';
                    }
                ?>
                    <<?= $page_tag ?> href="<?= $page_link_href ?>" class="<?= $page_link_class ?>" <?= $aria_current ?>>
                        <?= $i ?>
                    </<?= $page_tag ?>>
                <?php endfor; ?>

                <?php
                // Show ellipsis and last page if needed
                if ($end_page < $total_pages) {
                    if ($end_page < $total_pages - 1): ?>
                    <span class="px-1 sm:px-2 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-500">...</span>
                    <?php endif;

                    $last_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-300 hover:bg-gray-700';
                    $last_link_href = $base_url . 'p=' . $total_pages;
                ?>
                    <a href="<?= $last_link_href ?>" class="<?= $last_link_class ?>"><?= $total_pages ?></a>
                <?php
                }

                // Next page button
                $next_page = $current_page + 1;
                $next_link_class = 'px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-r-button ';
                $next_link_href = $base_url . 'p=' . $next_page;
                if ($current_page >= $total_pages) {
                    $next_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
                    $next_tag = 'span';
                    $next_link_href = '#';
                } else {
                    $next_link_class .= 'text-gray-300 hover:bg-gray-700';
                    $next_tag = 'a';
                }
                ?>
                <<?= $next_tag ?> href="<?= $next_link_href ?>" class="<?= $next_link_class ?>" <?= ($next_tag === 'a' ? 'aria-label="Next"' : '') ?>>
                     <i class="ri-arrow-right-s-line"></i>
                </<?= $next_tag ?>>
            </nav>
        </div>
        <?php endif; ?>

        <?php if ($product_count === 0 && empty($page_results)) : ?>
            <p class="text-center text-gray-400 text-lg">Nenhum resultado encontrado para "<?= htmlspecialchars($search_query) ?>".</p>
            <p class="text-center text-gray-500 mt-2">Tente pesquisar por outros termos em produtos ou páginas.</p>
        <?php endif; ?>
    <?php else : ?>
        <p class="text-center text-gray-400 text-lg">Nenhum resultado encontrado para "<?= htmlspecialchars($search_query) ?>".</p>
        <p class="text-center text-gray-500 mt-2">Tente pesquisar por outros termos em produtos ou páginas.</p>
    <?php endif; ?>
</div>

<!-- Add JavaScript to initialize product variations -->
<script>
// Initialize product variations for search results
document.addEventListener('DOMContentLoaded', function() {
    // Product variations data from PHP
    const productVariationsData = <?= json_encode($product_variations, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;
    const attributeValueModifiersData = <?= json_encode($attribute_value_modifiers, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;

    // Add event listener for add to cart buttons
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        const productId = button.dataset.productId;
        if (!productId) return;

        // Check if we have variations for this product
        if (productVariationsData[productId]) {
            const variations = productVariationsData[productId];
            const variationIds = Object.keys(variations);

            // If there's only one variation, check its stock and enable/disable the button accordingly
            if (variationIds.length === 1) {
                const variationId = variationIds[0];
                const variation = variations[variationId];

                // Debug output to console

                // Parse stock as integer to ensure proper comparison
                // Make sure we're dealing with a number by using parseInt and checking for NaN
                let stock = parseInt(variation.stock);
                if (isNaN(stock)) {
                    stock = 0; // Default to 0 if not a valid number
                }

                if (stock > 0) {
                    button.dataset.variationId = variationId;
                    button.classList.add('bg-primary');
                    button.classList.remove('bg-gray-800');
                    button.disabled = false;
                    button.classList.remove('opacity-50', 'cursor-not-allowed');
                    button.title = `Adicionar ao carrinho (${stock} disponíveis)`;
                } else {
                    button.disabled = true;
                    button.classList.add('opacity-50', 'cursor-not-allowed');
                    button.classList.remove('bg-primary');
                    button.classList.add('bg-gray-800');
                    button.title = 'Produto esgotado';
                }
            } else {
                // For products with multiple variations, disable the button and show a message
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.classList.remove('bg-primary');
                button.classList.add('bg-gray-800');
                button.title = 'Selecione as opções na página do produto';
            }
        }
    });
});
</script>

<?php

?>
