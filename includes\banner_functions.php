<?php

require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';

function get_banners($filters = []) {
    $pdo = get_db_connection();
    if (!$pdo) return [];
    
    $where_conditions = [];
    $params = [];
    
    if (isset($filters['banner_type'])) {
        $where_conditions[] = "banner_type = :banner_type";
        $params[':banner_type'] = $filters['banner_type'];
    }
    
    if (isset($filters['is_active'])) {
        $where_conditions[] = "is_active = :is_active";
        $params[':is_active'] = $filters['is_active'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    $order_by = isset($filters['order_by']) ? $filters['order_by'] : 'display_order ASC, created_at DESC';
    
    $sql = "SELECT * FROM banners {$where_clause} ORDER BY {$order_by}";
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

function get_banner_by_id($id) {
    $pdo = get_db_connection();
    if (!$pdo) return null;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM banners WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return null;
    }
}

function create_banner($data) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    $required_fields = ['title', 'image_filename', 'link_type', 'banner_type'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            return false;
        }
    }
    
    try {
        $sql = "INSERT INTO banners (title, image_filename, link_type, link_value, link_target, banner_type, is_active, display_order, created_at, updated_at) 
                VALUES (:title, :image_filename, :link_type, :link_value, :link_target, :banner_type, :is_active, :display_order, datetime('now', 'localtime'), datetime('now', 'localtime'))";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            ':title' => $data['title'],
            ':image_filename' => $data['image_filename'],
            ':link_type' => $data['link_type'],
            ':link_value' => $data['link_value'] ?? null,
            ':link_target' => $data['link_target'] ?? '_self',
            ':banner_type' => $data['banner_type'],
            ':is_active' => isset($data['is_active']) ? (int)$data['is_active'] : 1,
            ':display_order' => isset($data['display_order']) ? (int)$data['display_order'] : 0
        ]);
        
        return $result ? $pdo->lastInsertId() : false;
    } catch (PDOException $e) {
        return false;
    }
}

function update_banner($id, $data) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $sql = "UPDATE banners SET 
                title = :title,
                image_filename = :image_filename,
                link_type = :link_type,
                link_value = :link_value,
                link_target = :link_target,
                banner_type = :banner_type,
                is_active = :is_active,
                display_order = :display_order,
                updated_at = datetime('now', 'localtime')
                WHERE id = :id";
        
        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            ':id' => $id,
            ':title' => $data['title'],
            ':image_filename' => $data['image_filename'],
            ':link_type' => $data['link_type'],
            ':link_value' => $data['link_value'] ?? null,
            ':link_target' => $data['link_target'] ?? '_self',
            ':banner_type' => $data['banner_type'],
            ':is_active' => isset($data['is_active']) ? (int)$data['is_active'] : 1,
            ':display_order' => isset($data['display_order']) ? (int)$data['display_order'] : 0
        ]);
    } catch (PDOException $e) {
        return false;
    }
}

function delete_banner($id) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        
        $banner = get_banner_by_id($id);
        if ($banner && !empty($banner['image_filename'])) {
            $image_path = __DIR__ . '/../public/uploads/banners/' . $banner['image_filename'];
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        $stmt = $pdo->prepare("DELETE FROM banners WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        return false;
    }
}

function toggle_banner_status($id) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $sql = "UPDATE banners SET 
                is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END,
                updated_at = datetime('now', 'localtime')
                WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        return false;
    }
}

function get_active_banners($banner_type, $limit = null) {
    $filters = [
        'banner_type' => $banner_type,
        'is_active' => 1,
        'order_by' => 'display_order ASC, visitor_count ASC'
    ];
    
    $banners = get_banners($filters);
    
    if ($limit && count($banners) > $limit) {
        $banners = array_slice($banners, 0, $limit);
    }
    
    return $banners;
}

function increment_banner_visitor_count($id) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $stmt = $pdo->prepare("UPDATE banners SET visitor_count = visitor_count + 1 WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        return false;
    }
}

function reset_all_banner_visitor_counts() {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $stmt = $pdo->prepare("UPDATE banners SET visitor_count = 0");
        return $stmt->execute();
    } catch (PDOException $e) {
        return false;
    }
}

function get_banner_url($banner) {
    if ($banner['link_type'] === 'external') {
        return $banner['link_value'];
    } elseif ($banner['link_type'] === 'internal' && !empty($banner['link_value'])) {
        
        return BASE_URL . '/index.php?product=' . urlencode($banner['link_value']) . '&' . get_session_id_param();
    }
    return '#';
}

function get_products_for_banner_dropdown() {
    $pdo = get_db_connection();
    if (!$pdo) return [];
    
    try {
        $stmt = $pdo->prepare("SELECT id, name_pt, slug FROM products WHERE is_active = 1 ORDER BY name_pt ASC");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

function display_homepage_content_banner($css_class = 'mb-6') {
    return display_banners('homepage_content', $css_class);
}

function display_footer_banner($css_class = 'mb-6') {
    return display_banners('footer', $css_class);
}

function handle_banner_image_upload($file) {
    $upload_dir = __DIR__ . '/../public/uploads/banners/';
    
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/webm'];
    $max_size = 5 * 1024 * 1024; 
    
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'error' => 'Tipo de arquivo não permitido. Use JPG, PNG, WEBP, GIF ou WEBM.'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'error' => 'Arquivo muito grande. Máximo 5MB.'];
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'banner_' . uniqid() . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'error' => 'Erro ao fazer upload do arquivo.'];
    }
}

function cleanup_orphaned_banner_images() {
    global $pdo;
    
    $upload_dir = __DIR__ . '/../public/uploads/banners/';
    
    
    if (!is_dir($upload_dir)) {
        return ['success' => true, 'message' => 'Banner upload directory does not exist.'];
    }
    
    try {
        
        $stmt = $pdo->prepare("SELECT DISTINCT image_filename FROM banners WHERE image_filename IS NOT NULL AND image_filename != ''");
        $stmt->execute();
        $db_images = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        
        $files = scandir($upload_dir);
        $image_files = [];
        
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && is_file($upload_dir . $file)) {
                
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'webp', 'gif', 'webm'])) {
                    $image_files[] = $file;
                }
            }
        }
        
        
        $orphaned_files = array_diff($image_files, $db_images);
        $deleted_count = 0;
        $deleted_files = [];
        
        foreach ($orphaned_files as $orphaned_file) {
            $file_path = $upload_dir . $orphaned_file;
            if (file_exists($file_path) && unlink($file_path)) {
                $deleted_count++;
                $deleted_files[] = $orphaned_file;
            }
        }
        
        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'deleted_files' => $deleted_files,
            'message' => "Cleanup completed. Deleted {$deleted_count} orphaned banner images."
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Error during cleanup: ' . $e->getMessage()
        ];
    }
}

function get_banner_image_statistics() {
    global $pdo;
    
    $upload_dir = __DIR__ . '/../public/uploads/banners/';
    
    try {
        
        $stmt = $pdo->prepare("SELECT COUNT(DISTINCT image_filename) as db_count FROM banners WHERE image_filename IS NOT NULL AND image_filename != ''");
        $stmt->execute();
        $db_count = $stmt->fetchColumn();
        
        
        $fs_count = 0;
        $total_size = 0;
        
        if (is_dir($upload_dir)) {
            $files = scandir($upload_dir);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && is_file($upload_dir . $file)) {
                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'webp', 'gif', 'webm'])) {
                        $fs_count++;
                        $total_size += filesize($upload_dir . $file);
                    }
                }
            }
        }
        
        return [
            'success' => true,
            'database_images' => $db_count,
            'filesystem_images' => $fs_count,
            'potential_orphans' => max(0, $fs_count - $db_count),
            'total_size_bytes' => $total_size,
            'total_size_mb' => round($total_size / (1024 * 1024), 2)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Error getting statistics: ' . $e->getMessage()
        ];
    }
}