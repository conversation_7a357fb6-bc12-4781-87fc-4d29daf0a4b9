<?php
header('Content-Type: application/json');
error_reporting(E_ALL); 
ini_set('display_errors', 1); 

$audioDir = __DIR__ . '/public/assets/audio/'; 

if (!is_dir($audioDir)) {
    echo json_encode(['error' => 'Audio directory not found: ' . $audioDir]);
    exit;
}

$files = scandir($audioDir);
if ($files === false) {
    echo json_encode(['error' => 'Could not scan audio directory: ' . $audioDir]);
    exit;
}

$audioFiles = [];

foreach ($files as $file) {
    if ($file === '.' || $file === '..') {
        continue;
    }

    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    $filenameWithoutExt = pathinfo($file, PATHINFO_FILENAME);

    if ($extension === 'mp3' || $extension === 'm4a') {
        $txtFilePath = $audioDir . $filenameWithoutExt . '.txt';
        $descriptionText = 'No description available.'; 

        if (file_exists($txtFilePath)) {
            $content = file_get_contents($txtFilePath); 
            if ($content !== false) { 
                
                $descriptionText = strip_tags($content); 
            } else {
                
                $descriptionText = 'Error reading description file.';
                
                
            }
        }

        $audioFiles[] = [
            'audio' => 'public/assets/audio/' . $file, 
            
            'description' => trim($descriptionText), 
            'displayName' => $filenameWithoutExt
        ];
    }
}

if (empty($audioFiles)) {
    
    
    
    
    echo json_encode([]);
} else {
    echo json_encode($audioFiles);
}
?>