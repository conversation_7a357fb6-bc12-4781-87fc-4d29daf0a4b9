<![CDATA[
<?php

function migrate_rename_pages_show_in_header_column(PDO $pdo) {
    try {
        $pdo->beginTransaction();

        
        $stmt = $pdo->query("PRAGMA table_info(pages);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        $old_column_exists = in_array('show_in_headerINTEGER', $columns);
        $new_column_exists = in_array('show_in_header', $columns);

        if ($old_column_exists && !$new_column_exists) {
            
            
            
            $pdo->exec("ALTER TABLE pages RENAME COLUMN show_in_headerINTEGER TO show_in_header;");
            log_migration_applied($pdo, 'rename_pages_show_in_header_column'); 
        } elseif ($new_column_exists) {
            
            
            if (!$old_column_exists) {
                 
                 if (!is_migration_applied($pdo, 'rename_pages_show_in_header_column')) { 
                    log_migration_applied($pdo, 'rename_pages_show_in_header_column'); 
                 }
            } else {
            }
        } elseif (!$old_column_exists && !$new_column_exists) {
        }

        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        return false;
    }
}

