<?php
require_once 'includes/db.php';
require_once 'includes/digital_files_functions.php';

$pdo = get_db_connection();

function add_file_type_associations($digital_file_id, $file_type_ids) {
    global $pdo;
    
    
    $stmt = $pdo->prepare("DELETE FROM digital_file_type_associations WHERE digital_file_id = :digital_file_id");
    $stmt->execute([':digital_file_id' => $digital_file_id]);
    
    
    $stmt = $pdo->prepare("INSERT INTO digital_file_type_associations (digital_file_id, file_type_id) VALUES (:digital_file_id, :file_type_id)");
    
    foreach ($file_type_ids as $type_id) {
        $stmt->execute([
            ':digital_file_id' => $digital_file_id,
            ':file_type_id' => $type_id
        ]);
    }
    
    return true;
}

$stmt = $pdo->query("SELECT * FROM digital_files");
$files = $stmt->fetchAll(PDO::FETCH_ASSOC);

$stmt = $pdo->query("SELECT * FROM digital_files_file_types");
$file_types = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    if (isset($_POST['add_associations'])) {
        $digital_file_id = $_POST['digital_file_id'];
        $file_type_ids = $_POST['file_type_ids'] ?? [];
        
        if (add_file_type_associations($digital_file_id, $file_type_ids)) {
            echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin-bottom: 15px; border-radius: 4px;'>
                File type associations added successfully!
            </div>";
        } else {
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; margin-bottom: 15px; border-radius: 4px;'>
                Error adding file type associations.
            </div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add File Type Associations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h1, h2 {
            color: #343a40;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: left;
        }
        th {
            background-color: #e9ecef;
        }
        form {
            margin-bottom: 20px;
        }
        select, button {
            padding: 8px 12px;
            margin-bottom: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #0069d9;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            background-color: #f1f3f5;
            padding: 5px 10px;
            border-radius: 4px;
        }
        .checkbox-item input {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Add File Type Associations</h1>
        
        <h2>Add Associations</h2>
        <form method="post">
            <div>
                <label for="digital_file_id">Select Digital File:</label>
                <select name="digital_file_id" id="digital_file_id" required>
                    <option value="">-- Select a file --</option>
                    <?php foreach ($files as $file): ?>
                        <option value="<?= $file['id'] ?>">
                            <?= htmlspecialchars($file['display_name'] ?? $file['original_filename']) ?> 
                            (ID: <?= $file['id'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <p>Select File Types:</p>
                <div class="checkbox-group">
                    <?php foreach ($file_types as $type): ?>
                        <div class="checkbox-item">
                            <input type="checkbox" name="file_type_ids[]" id="type_<?= $type['id'] ?>" value="<?= $type['id'] ?>">
                            <label for="type_<?= $type['id'] ?>"><?= htmlspecialchars($type['name']) ?> (<?= htmlspecialchars($type['extension']) ?>)</label>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <button type="submit" name="add_associations">Add Associations</button>
        </form>
        
        <h2>Current Associations</h2>
        <table>
            <thead>
                <tr>
                    <th>File ID</th>
                    <th>File Name</th>
                    <th>Associated File Types</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($files as $file): 
                    $file_types = get_digital_file_file_types($file['id']);
                ?>
                <tr>
                    <td><?= $file['id'] ?></td>
                    <td><?= htmlspecialchars($file['display_name'] ?? $file['original_filename']) ?></td>
                    <td>
                        <?php if (empty($file_types)): ?>
                            <em>No file types associated</em>
                        <?php else: ?>
                            <ul style="margin: 0; padding-left: 20px;">
                                <?php foreach ($file_types as $type): ?>
                                    <li><?= htmlspecialchars($type['name']) ?> (<?= htmlspecialchars($type['extension']) ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
