<?php

require_once __DIR__ . '/../../includes/product_functions.php';
require_once __DIR__ . '/../../includes/vat_functions.php';
require_once __DIR__ . '/../../includes/custom_field_functions.php';
require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/coupon_functions.php';

$cart_items = $_SESSION['cart'] ?? [];
$cart_subtotal = 0.0;
$total_items_count = 0;

$vat_groups = [];

foreach ($cart_items as $key => &$item) {
    $item_total = $item['price'] * $item['quantity'];
    $cart_subtotal += $item_total;
    $total_items_count += $item['quantity'];
    $item['total_item_price'] = $item_total;

    
    if (!isset($item['vat_rate']) || !isset($item['vat_description'])) {
        
        $product_vat = get_product_vat_rate($item['product_id']);
        $item['vat_rate'] = $product_vat ? $product_vat['rate'] : 23.0;
        $item['vat_description'] = $product_vat ? $product_vat['description'] : 'Taxa Normal';
    }

    
    if (!isset($item['product_type'])) {
        $product_data = db_query("SELECT product_type FROM products WHERE id = ?", [$item['product_id']], true);
        $item['product_type'] = $product_data ? $product_data['product_type'] : 'regular';
    }

    
    $vat_rate = $item['vat_rate'];
    if (!isset($vat_groups[$vat_rate])) {
        $vat_groups[$vat_rate] = [
            'rate' => $vat_rate,
            'description' => $item['vat_description'],
            'subtotal' => 0,
            'tax' => 0
        ];
    }
    $vat_groups[$vat_rate]['subtotal'] += $item_total;
    $vat_groups[$vat_rate]['tax'] += $item_total * ($vat_rate / 100);

    
    $image_sql = "SELECT filename FROM product_images
                  WHERE product_id = ?
                  ORDER BY sort_order ASC LIMIT 1"; 
    $image_result = db_query($image_sql, [$item['product_id']], true);
    
    $item['image_url'] = $image_result && !empty($image_result['filename'])
                         ? get_product_image_url($image_result['filename'])
                         : get_asset_url('images/placeholder.png'); 

    
    
    if (isset($item['product_type']) && $item['product_type'] === 'digital') {
        
        $item['stock'] = 999;
    } else if ($item['variation_id'] !== null) {
        
        $stock_check = db_query("SELECT stock FROM product_variations WHERE id = ?", [$item['variation_id']], true);
        $item['stock'] = $stock_check ? ($stock_check['stock'] ?? 0) : 0;
    } else {
        
        $stock_check = db_query("SELECT stock FROM products WHERE id = ?", [$item['product_id']], true);
        $item['stock'] = $stock_check ? ($stock_check['stock'] ?? 0) : 0;
    }

    
    if (isset($item['product_slug']) && !empty($item['product_slug'])) {
        $product_slug = $item['product_slug'];
    } else {
        
        $product_slug_query = "SELECT slug FROM products WHERE id = ?";
        $product_slug_result = db_query($product_slug_query, [$item['product_id']], true);
        $product_slug = $product_slug_result ? $product_slug_result['slug'] : '';
    }
    $item['product_url'] = get_product_url($product_slug); 
}
unset($item);

$cart_totals = calculate_cart_totals();
$shipping_cost = $cart_totals['shipping_cost'];
$tax_amount = $cart_totals['tax_amount'];
$discount_amount = $cart_totals['discount_amount'];
$grand_total = $cart_totals['grand_total'];
$min_order_value = $cart_totals['min_order_value'];
$free_shipping_threshold = $cart_totals['free_shipping_threshold'];
$meets_min_order = $cart_totals['meets_min_order'];
$has_free_shipping = $cart_totals['has_free_shipping'];

$has_digital_products = false;
$only_digital_products = true;
foreach ($cart_items as $item) {
    if (isset($item['product_type']) && $item['product_type'] === 'digital') {
        $has_digital_products = true;
    } else {
        $only_digital_products = false;
    }
}
$only_digital_products = $has_digital_products && $only_digital_products;

$currency_symbol = get_setting('currency_symbol', '€');

$tax_rate_setting = get_setting('tax_rate', 0);
?>
<style>
/* It's generally better to move this to your main CSS file */
.svg-white-filter {
  filter: brightness(0) invert(1);
  /* This filter makes the SVG white */
}
</style>

<div class="flex items-center mb-6">
    <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="flex items-center text-gray-400 hover:text-white transition">
        <div class="w-5 h-5 flex items-center justify-center mr-1">
            <i class="ri-arrow-left-line"></i>
        </div>
        <span>Voltar à Loja</span>
    </a>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Cart Items Section -->
    <div class="lg:col-span-2">
        <div class="bg-gray-900 rounded-lg p-6">
            <h1 class="text-2xl font-semibold mb-6">Seu Carrinho (<span id="cart-page-item-count"><?= $total_items_count ?></span> items)</h1>

            <!-- Cart Items Container -->
            <div id="cartItemsContainer" class="space-y-6 <?= empty($cart_items) ? 'hidden' : '' ?>">
                <?php foreach ($cart_items as $cart_key => $item): ?>
                    <div class="cart-item-row flex flex-col sm:flex-row items-start sm:items-center border-b border-gray-800 pb-6"
                         data-cart-key="<?= $cart_key ?>"
                         data-product-id="<?= $item['product_id'] ?>"
                         data-variation-id="<?= $item['variation_id'] ?>"
                         data-price="<?= $item['price'] ?>">
                        <a href="<?= $item['product_url'] ?>">
                            <img src="<?= sanitize_input($item['image_url']) ?>" alt="<?= sanitize_input($item['name']) ?>" class="w-24 h-24 object-cover rounded mb-4 sm:mb-0 flex-shrink-0">
                        </a>
                        <div class="flex-1 sm:ml-6">
                            <div class="flex flex-col sm:flex-row sm:items-start justify-between mb-2">
                                <div>
                                    <h3 class="text-lg font-medium mb-1">
                                        <a href="<?= $item['product_url'] ?>" class="hover:text-primary transition"><?= sanitize_input($item['name']) ?></a>
                                        <?php if (isset($item['product_type']) && $item['product_type'] === 'digital'): ?>
                                            <span class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                                                <i class="ri-download-line mr-1"></i> Digital
                                            </span>
                                        <?php endif; ?>
                                    </h3>
                                    <?php if (!empty($item['attributes_display'])): ?>
                                        <p class="text-sm text-gray-400"><?= sanitize_input($item['attributes_display']) ?></p>
                                    <?php elseif (isset($item['variation_id']) && $item['variation_id'] !== null): ?>
                                        <?php
                                        
                                        require_once __DIR__ . '/../../includes/product_functions.php';
                                        $attributes_display = get_variation_attribute_string($item['variation_id']);
                                        if (!empty($attributes_display)):
                                        ?>
                                            <p class="text-sm text-gray-400"><?= sanitize_input($attributes_display) ?></p>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if (!empty($item['custom_fields'])): ?>
                                        <?php foreach ($item['custom_fields'] as $custom_field): ?>
                                            <p class="text-sm text-gray-400"><?= sanitize_input(get_cart_custom_field_display($custom_field)) ?></p>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                     <p class="text-sm text-gray-500">SKU: <?= sanitize_input($item['sku'] ?? 'N/D') ?></p>
                                </div>
                                <span class="text-lg font-semibold mt-2 sm:mt-0 item-price-display"><?= (isset($item['product_type']) && $item['product_type'] === 'digital' && $item['price'] == 0) ? 'Gratis' : format_price($item['price'], $currency_symbol) ?></span>
                            </div>
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex items-center">
                                    <button class="quantity-btn decrease-qty-btn" aria-label="Diminuir quantidade" data-cart-key="<?= $cart_key ?>">
                                        <i class="ri-subtract-line"></i>
                                    </button>
                                    <input type="number" value="<?= $item['quantity'] ?>" min="1" <?= (!isset($item['product_type']) || $item['product_type'] !== 'digital') ? 'max="' . $item['stock'] . '"' : '' ?> class="quantity-input mx-2 cart-item-qty" aria-label="Quantidade" data-cart-key="<?= $cart_key ?>">
                                    <button class="quantity-btn increase-qty-btn" aria-label="Aumentar quantidade" data-cart-key="<?= $cart_key ?>">
                                        <i class="ri-add-line"></i>
                                    </button>
                                    <?php if (!isset($item['product_type']) || $item['product_type'] !== 'digital'): ?>
                                     <span class="text-xs text-gray-500 ml-2">(<?= $item['stock'] ?> em stock)</span>
                                    <?php endif; ?>
                                </div>
                                <button class="remove-item-btn text-gray-400 hover:text-red-500 flex items-center transition" data-cart-key="<?= $cart_key ?>" title="Remover Item">
                                    <div class="w-5 h-5 flex items-center justify-center mr-1">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                    <span class="hidden sm:inline">Remover</span>
                                </button>
                            </div>
                             <p class="text-right text-gray-400 text-sm mt-2">Total Item: <span class="font-medium text-gray-200 item-total-price-display"><?= (isset($item['product_type']) && $item['product_type'] === 'digital' && $item['price'] == 0) ? 'Gratis' : format_price($item['total_item_price'], $currency_symbol) ?></span></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Empty Cart State -->
            <div id="emptyCartMessage" class="<?= empty($cart_items) ? '' : 'hidden' ?> text-center py-12">
                <div class="w-24 h-24 mx-auto mb-6 flex items-center justify-center bg-gray-800 rounded-full">
                    <i class="ri-shopping-cart-line text-4xl text-gray-400"></i>
                </div>
                <h2 class="text-xl font-medium mb-2">Seu carrinho está vazio</h2>
                <p class="text-gray-400 mb-6">Parece que ainda não adicionou nenhum item.</p>
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium whitespace-nowrap">Continuar a Comprar</a>
            </div>

            <!-- Action Buttons -->
            <div id="cartActionButtons" class="mt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 <?= empty($cart_items) ? 'hidden' : '' ?>">
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="flex items-center text-gray-300 hover:text-white transition">
                    <div class="w-5 h-5 flex items-center justify-center mr-1">
                        <i class="ri-arrow-left-line"></i>
                    </div>
                    <span>Continuar a Comprar</span>
                </a>
                <button id="clearCartBtn" class="text-gray-400 hover:text-red-500 flex items-center transition">
                    <div class="w-5 h-5 flex items-center justify-center mr-1">
                        <i class="ri-delete-bin-line"></i>
                    </div>
                    <span>Limpar Carrinho</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Order Summary Section -->
    <div id="orderSummary" class="lg:col-span-1 <?= empty($cart_items) ? 'hidden' : '' ?>">
        <div class="bg-gray-900 rounded-lg p-6 sticky top-24">
            <h2 class="text-xl font-semibold mb-6">Resumo do Pedido</h2>

            <div class="space-y-3 mb-6">
                <div class="flex justify-between">
                    <span class="text-gray-400">Subtotal</span>
                    <span id="summary-subtotal"><?= format_price($cart_subtotal, $currency_symbol) ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Envio</span>
                    <span id="summary-shipping">
                        <?php if ($only_digital_products): ?>
                            <span class="text-blue-400">Produto Digital</span>
                        <?php else: ?>
                            <?= $shipping_cost > 0 ? format_price($shipping_cost, $currency_symbol) : 'Grátis' ?>
                        <?php endif; ?>
                    </span>
                </div>
                <?php if ($has_digital_products): ?>
                <div class="flex justify-between text-sm text-blue-400">
                    <span><i class="ri-information-line mr-1"></i> Produtos digitais</span>
                    <span>Entrega por email</span>
                </div>
                <?php endif; ?>
                <?php if (count($vat_groups) <= 1): ?>
                    <div class="flex justify-between">
                        <span class="text-gray-400">IVA <?php
                            if (!empty($vat_groups)) {
                                $first_group = reset($vat_groups);
                                echo '(' . sanitize_input($first_group['description']) . ' ' . number_format($first_group['rate'], 1, ',') . '%)';
                            } else {
                                echo '(0%)';
                            }
                        ?></span>
                        <span id="summary-tax"><?= format_price($tax_amount, $currency_symbol) ?></span>
                    </div>
                <?php else: ?>
                    <div class="flex justify-between">
                        <span class="text-gray-400">IVA (Detalhado)</span>
                        <span id="summary-tax"><?= format_price($tax_amount, $currency_symbol) ?></span>
                    </div>
                    <?php foreach ($vat_groups as $group): ?>
                    <div class="flex justify-between pl-4 text-sm">
                        <span class="text-gray-500"><?= sanitize_input($group['description']) ?> <?= number_format($group['rate'], 1, ',') ?>%</span>
                        <span><?= format_price($group['tax'], $currency_symbol) ?></span>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                 <div id="summary-discount-row" class="flex justify-between text-green-400 <?= $discount_amount > 0 ? '' : 'hidden' ?>">
                    <span class="text-gray-400">
                        Desconto
                        <?php
                        
                        if (isset($_SESSION['applied_coupon_description']) && !empty($_SESSION['applied_coupon_description'])) {
                            echo ' <i>(' . sanitize_input($_SESSION['applied_coupon_description']) . ')</i>';
                        } elseif (isset($_SESSION['applied_promo_code']) && !empty($_SESSION['applied_promo_code'])) {
                            
                            echo ' <i>(' . sanitize_input($_SESSION['applied_promo_code']) . ')</i>';
                        }
                        ?>
                    </span>
                    <span id="summary-discount-amount">-<?= format_price($discount_amount, $currency_symbol) ?></span>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-4 mb-6">
                <div class="flex justify-between items-center">
                    <span class="font-semibold">Total</span>
                    <span class="text-xl font-bold" id="summary-total"><?= format_price($grand_total, $currency_symbol) ?></span>
                </div>

                <?php if ($min_order_value > 0): ?>
                <div class="mt-3 <?= $meets_min_order ? 'text-green-500' : 'text-yellow-500' ?>" data-min-order-indicator>
                    <div class="flex items-center text-sm">
                        <i class="<?= $meets_min_order ? 'ri-checkbox-circle-fill' : 'ri-error-warning-fill' ?> mr-1"></i>
                        <?php if ($meets_min_order): ?>
                            <span>Valor mínimo para checkout atingido</span>
                        <?php else: ?>
                            <span>Valor mínimo para checkout: <?= format_price($min_order_value, $currency_symbol) ?> <i>(Portes Incluídos)</i></span>
                        <?php endif; ?>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                        <div class="<?= $meets_min_order ? 'bg-green-500' : 'bg-yellow-500' ?> h-1.5 rounded-full" data-min-order-progress style="width: <?= $meets_min_order ? 100 : min(100, ($grand_total / $min_order_value) * 100) ?>%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1" data-min-order-remaining>
                        <?php if ($meets_min_order): ?>
                            Valor mínimo para checkout atingido!
                        <?php else: ?>
                            Faltam <?= format_price($min_order_value - $grand_total, $currency_symbol) ?> para atingir o valor mínimo
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($free_shipping_threshold > 0): ?>
                <div class="mt-3 <?= $has_free_shipping ? 'text-green-500' : 'text-gray-400' ?>" data-free-shipping-indicator>
                    <div class="flex items-center text-sm">
                        <i class="<?= $has_free_shipping ? 'ri-checkbox-circle-fill' : 'ri-truck-line' ?> mr-1"></i>
                        <?php if ($has_free_shipping): ?>
                            <span>Envio gratuito aplicado!</span>
                        <?php else: ?>
                            <span>Envio gratuito a partir de <?= format_price($free_shipping_threshold, $currency_symbol) ?> <i>(Portes Excluídos)</i></span>
                        <?php endif; ?>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                        <div class="<?= $has_free_shipping ? 'bg-green-500' : 'bg-primary' ?> h-1.5 rounded-full" data-free-shipping-progress style="width: <?= $has_free_shipping ? 100 : min(100, ($cart_subtotal / $free_shipping_threshold) * 100) ?>%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1" data-free-shipping-remaining>
                        <?php if ($has_free_shipping): ?>
                            Portes gratuitos desbloqueados!
                        <?php else: ?>
                            Faltam <?= format_price($free_shipping_threshold - $cart_subtotal, $currency_symbol) ?> para envio gratuito <i>(produtos físicos)</i>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="mb-6">
                <label for="promoCodeInput" class="block text-sm font-medium text-gray-400 mb-2">Código Promocional</label>
                <div class="flex">
                <input type="text" id="promoCodeInput" placeholder="Inserir código aqui" class="promo-input flex-1 rounded-l-lg <?= isset($_SESSION['applied_promo_code']) ? '' : 'rounded-r-none' ?> focus:ring-1 focus:ring-primary text-gray-900 text-center" value="<?= sanitize_input($_SESSION['applied_promo_code'] ?? '') ?>">
                    <?php if (isset($_SESSION['applied_promo_code'])): ?>
                        <button id="removePromoBtn" class="bg-red-700 hover:bg-red-600 text-white px-4 py-2 rounded-r-lg whitespace-nowrap">Remover</button>
                    <?php else: ?>
                        <button id="applyPromoBtn" class="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-r-lg whitespace-nowrap">Aplicar</button>
                    <?php endif; ?>
                </div>
                <div id="promoMessage" class="mt-2 text-sm"><!-- Messages appear here --></div>
            </div>

            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=checkout') ?>" id="checkoutBtnLink" class="block w-full bg-primary hover:bg-primary/90 text-white text-center py-3 px-6 rounded-button font-medium mb-4 whitespace-nowrap">
                Finalizar Compra
            </a>

            <div class="flex items-center justify-center space-x-6 text-sm text-gray-400">
                <div class="flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-1">
                        <i class="ri-shield-check-line"></i>
                    </div>
                    <span>Checkout Seguro</span>
                </div>
                <div class="flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-1">
                        <i class="ri-truck-line"></i>
                    </div>
                    <span>Envio Rápido</span> <!-- Adjusted text -->
                </div>
            </div>

            <div class="mt-6 flex justify-center space-x-3">
                <div class="w-10 h-6 flex items-center justify-center text-gray-400" title="MB WAY">
                    <img src="/public/assets/images/icons/mbway.svg" alt="MB WAY" class="h-full object-contain svg-white-filter" />
                </div>
                <div class="w-10 h-6 flex items-center justify-center text-gray-400" title="Multibanco">
                    <img src="/public/assets/images/icons/credit-card.svg" alt="Multibanco" class="h-full object-contain svg-white-filter" />
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modals (Structure from gui/checkout.html) -->
<div id="confirmModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[110] hidden">
    <div class="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl border border-gray-700">
        <h3 class="text-xl font-semibold mb-4">Remover Item</h3>
        <p class="text-gray-300 mb-6">Tem a certeza que quer remover este item do carrinho?</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelRemove" class="px-4 py-2 bg-gray-800 text-white rounded-button hover:bg-gray-700 whitespace-nowrap">Cancelar</button>
            <button id="confirmRemove" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 whitespace-nowrap">Remover</button>
        </div>
    </div>
</div>

<div id="clearCartModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[110] hidden">
     <div class="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl border border-gray-700">
        <h3 class="text-xl font-semibold mb-4">Limpar Carrinho</h3>
        <p class="text-gray-300 mb-6">Tem a certeza que quer remover todos os items do carrinho?</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelClear" class="px-4 py-2 bg-gray-800 text-white rounded-button hover:bg-gray-700 whitespace-nowrap">Cancelar</button>
            <button id="confirmClear" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 whitespace-nowrap">Limpar Carrinho</button>
        </div>
    </div>
</div>

<!-- Add Cart Specific JS -->
<script src="<?= get_asset_url('js/cart.js') ?>"></script>
<script>
// Define showToast function if it doesn't exist
if (typeof showToast !== 'function') {
    function showToast(title, message, icon = 'ri-information-line', borderClass = 'border-blue-500') {
        const toastContainer = document.getElementById('toast-container') || (() => {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
            document.body.appendChild(container);
            return container;
        })();

        const toast = document.createElement('div');
        toast.className = `bg-gray-900 border-l-4 ${borderClass} text-white p-4 rounded shadow-lg flex items-start max-w-xs transform transition-all duration-300 ease-out translate-x-full opacity-0`;

        toast.innerHTML = `
            <div class="mr-3 text-xl"><i class="${icon}"></i></div>
            <div class="flex-1">
                <div class="font-semibold">${title}</div>
                <div class="text-sm text-gray-300">${message}</div>
            </div>
            <button class="ml-2 text-gray-400 hover:text-white" onclick="this.parentElement.remove()">
                <i class="ri-close-line"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 10);

        // Auto-remove after 5 seconds
        const timeout = setTimeout(() => {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => toast.remove(), 300);
        }, 5000);

        // Return object with close method
        return {
            close: function() {
                clearTimeout(timeout);
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => toast.remove(), 300);
            }
        };
    }
}

document.addEventListener('DOMContentLoaded', function() {

    const currencySymbol = '<?= $currency_symbol ?>'; // Make symbol available to JS

    // --- Helper to format price ---
    function formatPriceJS(amount) {
        // Ensure consistent currency symbol position (always in front)
        return currencySymbol + parseFloat(amount).toFixed(2).replace('.', ',');
    }

    // --- Define updateCartCounter function locally if not available globally ---
    function updateCartCounter(count) {
        // Update cart count in header
        const cartCounter = document.getElementById('cart-item-count');
        if (cartCounter) {
            cartCounter.textContent = count;
            cartCounter.style.display = count > 0 ? 'flex' : 'none';
        }

        // Also update global cart count if the function exists
        if (window.updateCartCount && typeof window.updateCartCount === 'function') {
            window.updateCartCount(count);
        }
    }

    // --- Define removeFromCart function locally if not available globally ---
    // This inline function is removed as its functionality and event handling
    // are now solely managed by the external public/assets/js/cart.js script.
    // async function removeFromCart(productId, variationId = null, fromPreview = false) { ... }

    // --- Update Cart Totals Display ---
    function updateCartTotalsDisplay(subtotal, tax, shipping, total, discount = 0, vatGroups = null) {
        document.getElementById('summary-subtotal').textContent = formatPriceJS(subtotal);
        document.getElementById('summary-tax').textContent = formatPriceJS(tax);
        document.getElementById('summary-shipping').textContent = shipping > 0 ? formatPriceJS(shipping) : 'Grátis';
        document.getElementById('summary-total').textContent = formatPriceJS(total);

        // If VAT groups are provided, update the VAT breakdown
        if (vatGroups && typeof vatGroups === 'object') {
            // This would require server-side changes to return VAT groups in AJAX responses
            // For now, we'll just reload the page if VAT groups change
            // Future enhancement: dynamically update VAT breakdown without page reload
        }

        const discountRow = document.getElementById('summary-discount-row');
        const discountAmountEl = document.getElementById('summary-discount-amount');
        if (discount > 0 && discountRow && discountAmountEl) {
            discountAmountEl.textContent = '-' + formatPriceJS(discount);
            discountRow.classList.remove('hidden');

            // Note: We don't update the coupon description here because it would require
            // additional server data. Instead, we'll refresh the page when applying/removing coupons.
        } else if (discountRow) {
            discountRow.classList.add('hidden');
        }

        // Update item count display
        let currentTotalItems = 0;
         document.querySelectorAll('.cart-item-row').forEach(row => {
            const qtyInput = row.querySelector('.cart-item-qty');
            if(qtyInput) {
                currentTotalItems += parseInt(qtyInput.value) || 0;
            }
        });
        document.getElementById('cart-page-item-count').textContent = currentTotalItems;
        updateCartCounter(currentTotalItems); // Update header counter too

        // Hide summary if cart is empty
        const orderSummary = document.getElementById('orderSummary');
        const cartActionButtons = document.getElementById('cartActionButtons');
        if (currentTotalItems === 0) {
            orderSummary.classList.add('hidden');
            cartActionButtons.classList.add('hidden');
            document.getElementById('cartItemsContainer').classList.add('hidden');
            document.getElementById('emptyCartMessage').classList.remove('hidden');
        } else {
             orderSummary.classList.remove('hidden');
             cartActionButtons.classList.remove('hidden');
             document.getElementById('cartItemsContainer').classList.remove('hidden');
             document.getElementById('emptyCartMessage').classList.add('hidden');
        }
    }

    // --- Update Item Total Price Display ---
     function updateItemTotalDisplay(cartKey, quantity) {
        const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
        if (itemRow) {
            const price = parseFloat(itemRow.getAttribute('data-price'));
            const itemTotalDisplay = itemRow.querySelector('.item-total-price-display');
            if (!isNaN(price) && itemTotalDisplay) {
                itemTotalDisplay.textContent = formatPriceJS(price * quantity);
            }
        }
    }

    // --- Handle Quantity Change ---
    async function handleQuantityChange(cartKey, newQuantity) {
        const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
        const input = itemRow ? itemRow.querySelector('.cart-item-qty') : null;
        if (!itemRow || !input) return;

        // Store original quantity for error recovery
        const originalQuantity = parseInt(input.value) || 1;

        // Check if this is a digital product
        const isDigital = itemRow.querySelector('.bg-blue-900.text-blue-200') !== null;

        if (newQuantity < 1) newQuantity = 1;

        // Digital products are limited to 1 item
        if (isDigital && newQuantity > 1) {
            newQuantity = 1;
            showToast('Aviso', 'Produtos digitais são limitados a 1 unidade por pedido.', 'ri-error-warning-line', 'border-yellow-500');
            input.value = newQuantity; // Update input visually
            updateItemTotalDisplay(cartKey, newQuantity);
            return; // Prevent AJAX call to avoid duplicate error messages
        }
        // Only check stock limits for non-digital products
        else if (!isDigital && input.hasAttribute('max')) {
            const stock = parseInt(input.max);
            if (newQuantity > stock) {
                newQuantity = stock;
                showToast('Aviso', 'Quantidade máxima em stock atingida.', 'ri-error-warning-line', 'border-yellow-500');
            }
        }

        input.value = newQuantity; // Update input visually

        updateItemTotalDisplay(cartKey, newQuantity);

        // AJAX call to update session using ajaxRequest for consistency
        try {
            const response = await ajaxRequest('?action=update_cart_item', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    cart_key: cartKey,
                    quantity: newQuantity
                })
            });

            if (response.success) {
                // Update summary totals based on server response
                updateCartTotalsDisplay(response.subtotal, response.tax, response.shipping, response.total, response.discount);
                // Update header counter is handled within updateCartTotalsDisplay
            } else {
                showToast('Erro', response.error || 'Não foi possível atualizar a quantidade.', 'ri-error-warning-line', 'border-red-500');
                // Revert quantity visually
                input.value = response.original_quantity || originalQuantity;
                // Fetch current state to revert accurately if the function exists
                if (typeof fetchCartPreview === 'function') {
                    fetchCartPreview();
                }
            }
        } catch (error) {
            showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            // Revert to original quantity on error
            input.value = originalQuantity;
        }
    }

    // --- Event Listeners for Quantity Buttons ---
    document.getElementById('cartItemsContainer').addEventListener('click', function(e) {
        const decreaseBtn = e.target.closest('.decrease-qty-btn');
        const increaseBtn = e.target.closest('.increase-qty-btn');

        if (decreaseBtn) {
            const cartKey = decreaseBtn.getAttribute('data-cart-key');
            const input = document.querySelector(`.cart-item-qty[data-cart-key="${cartKey}"]`);
            if (input) {
                handleQuantityChange(cartKey, parseInt(input.value) - 1);
            }
        } else if (increaseBtn) {
            const cartKey = increaseBtn.getAttribute('data-cart-key');
            const input = document.querySelector(`.cart-item-qty[data-cart-key="${cartKey}"]`);
            if (input) {
                const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
                const isDigital = itemRow ? itemRow.querySelector('.bg-blue-900.text-blue-200') !== null : false;

                // For digital products, prevent increasing beyond 1 directly without AJAX call
                if (isDigital && parseInt(input.value) >= 1) {
                    // Don't show toast here - it will be handled by handleQuantityChange
                    return;
                }

                handleQuantityChange(cartKey, parseInt(input.value) + 1);
            }
        }
    });

    // --- Event Listener for Quantity Input Manual Change ---
     document.getElementById('cartItemsContainer').addEventListener('change', function(e) {
         if (e.target.classList.contains('cart-item-qty')) {
             const cartKey = e.target.getAttribute('data-cart-key');
             const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
             const isDigital = itemRow ? itemRow.querySelector('.bg-blue-900.text-blue-200') !== null : false;

             // For digital products, enforce quantity = 1 directly without AJAX call
             if (isDigital) {
                 const newValue = parseInt(e.target.value);
                 if (newValue > 1) {
                     e.target.value = 1;
                     showToast('Aviso', 'Produtos digitais são limitados a 1 unidade por pedido.', 'ri-error-warning-line', 'border-yellow-500');
                     updateItemTotalDisplay(cartKey, 1);
                     return;
                 }
             }

             handleQuantityChange(cartKey, parseInt(e.target.value));
         }
     });

    // --- Handle Remove Item ---
    // This inline event handling block is removed.
    // Event handling for remove buttons, including modal interaction,
    // is now solely managed by the external public/assets/js/cart.js script
    // which listens for clicks on elements with class '.remove-item-btn'.

     // --- Update Totals Based on DOM (for immediate UI feedback after removal) ---
     function updateCartTotalsDisplayBasedOnDOM() {
        let currentSubtotal = 0;
        let currentTotalItems = 0;
        document.querySelectorAll('.cart-item-row').forEach(row => {
            const price = parseFloat(row.getAttribute('data-price'));
            const qtyInput = row.querySelector('.cart-item-qty');
            const quantity = qtyInput ? (parseInt(qtyInput.value) || 0) : 0;
            if (!isNaN(price) && quantity > 0) {
                currentSubtotal += price * quantity;
                currentTotalItems += quantity;
            }
        });

        // Recalculate tax/shipping/total based on this potentially temporary subtotal
        const taxRate = parseFloat(<?= isset($tax_rate_setting) ? $tax_rate_setting : 0 ?>) / 100.0;
        const shipping = parseFloat(<?= isset($shipping_cost) ? $shipping_cost : 0 ?>);
        const currentTax = currentSubtotal * taxRate;
        // Assume discount needs to be reapplied by server, ignore for DOM update
        const currentTotal = currentSubtotal + currentTax + shipping;

        updateCartTotalsDisplay(currentSubtotal, currentTax, shipping, currentTotal, 0); // Pass 0 for discount initially
    }

    // --- Handle Clear Cart ---
    const clearCartModal = document.getElementById('clearCartModal');
    const cancelClearBtn = document.getElementById('cancelClear');
    const confirmClearBtn = document.getElementById('confirmClear');
    const clearCartBtn = document.getElementById('clearCartBtn');

     if (clearCartBtn) {
        clearCartBtn.addEventListener('click', () => {
            // Only show modal if there are items
            if (document.querySelectorAll('.cart-item-row').length > 0) {
                 clearCartModal.classList.remove('hidden');
            }
        });
    }

    cancelClearBtn.addEventListener('click', () => {
        clearCartModal.classList.add('hidden');
    });

    confirmClearBtn.addEventListener('click', async () => {
        clearCartModal.classList.add('hidden');

        // AJAX call to clear cart using ajaxRequest for consistency
        try {
            const response = await ajaxRequest('?action=clear_cart', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });

            if (response.success) {
                showToast('Sucesso', 'Carrinho limpo.');
                // Update UI: Hide items, show empty message, hide summary, update counters
                document.getElementById('cartItemsContainer').innerHTML = ''; // Clear items visually
                updateCartTotalsDisplay(0, 0, 0, 0); // Update totals to zero
            } else {
                showToast('Erro', response.error || 'Não foi possível limpar o carrinho.', 'ri-error-warning-line', 'border-red-500');
            }
        } catch (error) {
            showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
        }
    });

    // --- Handle Promo Code ---
    const applyPromoBtn = document.getElementById('applyPromoBtn');
    const removePromoBtn = document.getElementById('removePromoBtn');
    const promoInput = document.getElementById('promoCodeInput');
    const promoMessage = document.getElementById('promoMessage');

    // Apply promo code
    if (applyPromoBtn) {
        applyPromoBtn.addEventListener('click', async function() {
            const code = promoInput.value.trim();
            if (!code) {
                promoMessage.textContent = 'Por favor, insira um código.';
                promoMessage.className = 'mt-2 text-sm text-yellow-400';
                return;
            }

            try {
                const response = await ajaxRequest('?action=apply_promo_code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        promo_code: code
                    })
                });

                if (response.success) {
                    promoMessage.textContent = response.message || 'Código promocional aplicado!';
                    promoMessage.className = 'mt-2 text-sm text-green-400';

                    // Show success message
                    showToast('Sucesso', 'Código promocional aplicado! Atualizando página...', 'ri-check-line', 'border-green-500');

                    // Refresh the page to reflect all changes
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    promoMessage.textContent = response.error || 'Código promocional inválido ou expirado.';
                    promoMessage.className = 'mt-2 text-sm text-red-400';
                    // Update totals display without discount (or with previous discount if any)
                    updateCartTotalsDisplay(response.subtotal, response.tax, response.shipping, response.total, response.discount_amount);
                    // Clear input if invalid?
                    // promoInput.value = '';
                }
            } catch (error) {
                promoMessage.textContent = 'Erro de rede ao aplicar código.';
                promoMessage.className = 'mt-2 text-sm text-red-400';
            }
        });
    }

    // Remove promo code
    if (removePromoBtn) {
        removePromoBtn.addEventListener('click', async function() {
            try {
                // Show loading indicator
                const loadingToast = showToast('Processando', 'Removendo código promocional...', 'ri-loader-4-line animate-spin', 'border-blue-500');

                const response = await ajaxRequest('?action=apply_promo_code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        promo_code: '' // Empty string to remove the code
                    })
                });

                if (loadingToast) {
                    loadingToast.close();
                }

                if (response.success) {
                    promoMessage.textContent = response.message || 'Código promocional removido!';
                    promoMessage.className = 'mt-2 text-sm text-green-400';

                    // Show success message
                    showToast('Sucesso', 'Código promocional removido! Atualizando página...', 'ri-check-line', 'border-green-500');

                    // Refresh the page to reflect all changes
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    promoMessage.textContent = response.error || 'Não foi possível remover o código promocional.';
                    promoMessage.className = 'mt-2 text-sm text-red-400';
                }
            } catch (error) {
                promoMessage.textContent = 'Erro de rede ao remover código.';
                promoMessage.className = 'mt-2 text-sm text-red-400';
            }
        });
    }

    // --- Initial Check for Applied Promo Code Message ---
    <?php if (isset($_SESSION['promo_message'])): ?>
        promoMessage.textContent = '<?= sanitize_input($_SESSION['promo_message']['text']) ?>';
        promoMessage.className = 'mt-2 text-sm <?= $_SESSION['promo_message']['type'] === 'success' ? 'text-green-400' : 'text-red-400' ?>';
        <?php unset($_SESSION['promo_message']); ?>
    <?php endif; ?>

});
</script>
