<?php

header('Content-Type: application/json');

ini_set('display_errors', 0);
error_reporting(E_ALL);

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$maxRetries = 3;
$retryDelay = 5;
$urlProcessDelay = 1000; 
$pingSitesFile = __DIR__ . '/backlink_sites.php';

$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

if (!isset($data['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing action parameter']);
    exit;
}

require_once($pingSitesFile);

switch ($data['action']) {
    case 'fetch_urls':
        fetchUrls($data);
        break;
    case 'get_random_backlink_sites':
        getRandomBacklinkSites($data);
        break;
    case 'ping_url':
        pingUrl($data);
        break;
    case 'get_ping_sites':
        getPingSites();
        break;
    case 'save_ping_sites':
        savePingSites($data);
        break;
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

function fetchUrls($data) {
    global $maxRetries, $retryDelay;
    
    if (!isset($data['feedUrl']) || !isset($data['sitemapUrl'])) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        return;
    }

    $feedUrl = $data['feedUrl'];
    $sitemapUrl = $data['sitemapUrl'];
    $maxUrlsToProcess = isset($data['maxUrlsToProcess']) ? intval($data['maxUrlsToProcess']) : 0;
    
    
    if ($maxUrlsToProcess < 0) $maxUrlsToProcess = 0;
    
    
    
    $feedUrls = [];
    $sitemapUrls = [];
    $errors = [];
    $skippedUrls = [];

    
    if (!empty($feedUrl)) {
        $feedContent = fetchContent($feedUrl, $maxRetries, $retryDelay);
        if ($feedContent === false) {
            $errors[] = "Error fetching feed content from $feedUrl";
        } else {
            $feedXml = @simplexml_load_string($feedContent);
            if ($feedXml === false) {
                $errors[] = "Error parsing XML from feed file";
            } else {
                if (isset($feedXml->channel->item)) {
                    foreach ($feedXml->channel->item as $item) {
                        
                        if (isset($item->link)) {
                            $linkUrl = trim((string)$item->link);
                            if (!empty($linkUrl)) {
                                
                                if (strpos($linkUrl, 'cesta.html') !== false) {
                                    $skippedUrls[] = $linkUrl;
                                } else {
                                    $feedUrls[] = $linkUrl;
                                }
                            }
                        }
                        
                        if (isset($item->guid)) {
                            $guidUrl = trim((string)$item->guid);
                            if (!empty($guidUrl)) {
                                
                                if (strpos($guidUrl, 'cesta.html') !== false) {
                                    $skippedUrls[] = $guidUrl;
                                } else {
                                    $feedUrls[] = $guidUrl;
                                }
                            }
                        }
                        
                        
                        $contentFields = ['description', 'content', 'content:encoded', 'summary'];
                        foreach ($contentFields as $field) {
                            if (isset($item->$field) || isset($item->children('content', true)->encoded)) {
                                
                                $content = isset($item->$field) ? (string)$item->$field : (string)$item->children('content', true)->encoded;
                                if (!empty($content)) {
                                    
                                    preg_match_all('/href=["\'](https?:\/\/[^"\']+)["\']/', $content, $matches);
                                    if (isset($matches[1]) && !empty($matches[1])) {
                                        foreach ($matches[1] as $url) {
                                            $url = trim($url);
                                            if (!empty($url)) {
                                                
                                                if (strpos($url, 'cesta.html') !== false) {
                                                    $skippedUrls[] = $url;
                                                } else {
                                                    $feedUrls[] = $url;
                                                }
                                            }
                                        }
                                    }
                                    
                                    
                                    preg_match_all('/(https?:\/\/[^\s"\'<>]+\.[^\s"\'<>]+)/', $content, $directMatches);
                                    if (isset($directMatches[1]) && !empty($directMatches[1])) {
                                        foreach ($directMatches[1] as $url) {
                                            $url = trim($url);
                                            
                                            $url = rtrim($url, '.,;:!?)]}');
                                            if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                                                
                                                if (strpos($url, 'cesta.html') !== false) {
                                                    $skippedUrls[] = $url;
                                                } else {
                                                    $feedUrls[] = $url;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        
                        foreach ($item as $fieldName => $fieldValue) {
                            
                            if (in_array($fieldName, ['link', 'guid', 'description', 'content', 'summary'])) {
                                continue;
                            }
                            
                            $fieldStr = trim((string)$fieldValue);
                            
                            if (!empty($fieldStr)) {
                                
                                if (filter_var($fieldStr, FILTER_VALIDATE_URL)) {
                                    
                                    if (strpos($fieldStr, 'cesta.html') !== false) {
                                        $skippedUrls[] = $fieldStr;
                                    } else {
                                        $feedUrls[] = $fieldStr;
                                    }
                                }
                                
                                
                                preg_match_all('/(https?:\/\/[^\s"\'<>]+\.[^\s"\'<>]+)/', $fieldStr, $matches);
                                if (isset($matches[1]) && !empty($matches[1])) {
                                    foreach ($matches[1] as $url) {
                                        $url = trim($url);
                                        
                                        $url = rtrim($url, '.,;:!?)]}');
                                        if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                                            
                                            if (strpos($url, 'cesta.html') !== false) {
                                                $skippedUrls[] = $url;
                                            } else {
                                                $feedUrls[] = $url;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    
    if (!empty($sitemapUrl)) {
        $sitemapContent = fetchContent($sitemapUrl, $maxRetries, $retryDelay);
        if ($sitemapContent === false) {
            $errors[] = "Error fetching sitemap content from $sitemapUrl";
        } else {
            $sitemapXml = @simplexml_load_string($sitemapContent);
            if ($sitemapXml === false) {
                $errors[] = "Error parsing XML from sitemap file";
            } else {
                
                foreach ($sitemapXml->url as $urlNode) {
                    if (isset($urlNode->loc)) {
                        $url = trim((string)$urlNode->loc);
                        if (!empty($url)) {
                            
                            if (strpos($url, 'cesta.html') !== false) {
                                $skippedUrls[] = $url;
                            } else {
                                $sitemapUrls[] = $url;
                            }
                        }
                    }
                }
            }
        }
    }

    
    $allUrls = array_unique(array_merge($feedUrls, $sitemapUrls));
    
    
    $totalUrls = count($allUrls);
    
    
    $limitedUrls = $allUrls;
    $limitApplied = false;
    
    
    if ($maxUrlsToProcess > 0 && $maxUrlsToProcess < $totalUrls) {
        $limitedUrls = array_slice($allUrls, 0, $maxUrlsToProcess);
        $limitApplied = true;
    } else {
        
        $maxUrlsToProcess = $totalUrls;
    }

    if (empty($allUrls) && !empty($errors)) {
        echo json_encode(['success' => false, 'error' => implode('; ', $errors)]);
    } else {
        echo json_encode([
            'success' => true, 
            'urls' => array_values($limitedUrls),
            'feedCount' => count($feedUrls),
            'sitemapCount' => count($sitemapUrls),
            'totalCount' => $totalUrls,
            'processCount' => count($limitedUrls),
            'limitApplied' => $limitApplied,
            'maxUrlsToProcess' => $maxUrlsToProcess,
            'skippedUrls' => $skippedUrls,
            'skippedCount' => count($skippedUrls),
            'errors' => $errors
        ]);
    }
}

function getRandomBacklinkSites($data) {
    global $backlink_sites;
    
    $count = isset($data['count']) ? intval($data['count']) : 3;
    if ($count < 1) $count = 1;
    
    if ($count > count($backlink_sites)) $count = count($backlink_sites);
    
    
    $keys = array_keys($backlink_sites);
    
    
    if (empty($keys)) {
        echo json_encode([
            'success' => true,
            'sites' => [],
            'count' => 0
        ]);
        return;
    }
    
    
    shuffle($keys);
    $randomSites = [];
    
    
    $numSites = min(count($backlink_sites), $count);
    
    
    for ($i = 0; $i < $numSites; $i++) {
        $key = $keys[$i];
        $randomSites[$key] = $backlink_sites[$key];
    }
    
    echo json_encode([
        'success' => true,
        'sites' => $randomSites,
        'count' => count($randomSites)
    ]);
}

function pingUrl($data) {
    global $maxRetries, $retryDelay, $urlProcessDelay;
    
    if (!isset($data['url']) || !isset($data['backlinkSite']) || !isset($data['backlinkUrl'])) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        return;
    }

    $url = $data['url'];
    $backlinkSite = $data['backlinkSite'];
    $backlinkUrl = $data['backlinkUrl'];
    
    
    if (strpos($url, 'cesta.html') !== false) {
        echo json_encode([
            'success' => false,
            'error' => 'Skipped known problematic URL',
            'skipped' => true
        ]);
        return;
    }
    
    
    $pingUrl = $backlinkUrl;
    
    
    if (strpos($pingUrl, '?') === false) {
        $pingUrl .= '?url=' . urlencode($url);
    } else {
        $pingUrl .= '&url=' . urlencode($url);
    }
    
    
    $pingUrl .= '&title=' . urlencode('Website Update');
    
    
    $attempts = 0;
    $success = false;
    $httpCode = 0;
    $response = '';
    $curlError = '';
    $duration = 0;
    $isNonResponsive = false;
    
    while ($attempts < $maxRetries && !$success) {
        if ($attempts > 0) {
            
            sleep($retryDelay);
        }
        
        $attempts++;
        
        $ch = curl_init($pingUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; BacklinkPinger/1.0)');
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        
        if ($httpCode == 503) {
            continue; 
        }
        
        
        if ($curlError && (strpos($curlError, 'timed out') !== false || 
                          strpos($curlError, 'connection') !== false)) {
            $isNonResponsive = true;
        }
        
        
        if (empty($curlError) && ($httpCode >= 200 && $httpCode < 400)) {
            $success = true;
        }
    }
    
    
    usleep($urlProcessDelay * 1000); 
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'httpCode' => $httpCode,
            'response' => substr($response, 0, 255) . (strlen($response) > 255 ? '...' : ''),
            'duration' => $duration,
            'attempts' => $attempts
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $curlError ? $curlError : "HTTP Code: $httpCode",
            'httpCode' => $httpCode,
            'duration' => $duration,
            'attempts' => $attempts,
            'isNonResponsive' => $isNonResponsive
        ]);
    }
}

function getPingSites() {
    global $backlink_sites;
    
    echo json_encode([
        'success' => true,
        'sites' => $backlink_sites,
        'count' => count($backlink_sites)
    ]);
}

function savePingSites($data) {
    global $pingSitesFile;
    
    if (!isset($data['sites']) || !is_array($data['sites'])) {
        echo json_encode(['success' => false, 'error' => 'Missing or invalid sites parameter']);
        return;
    }
    
    $sites = $data['sites'];
    
    
    $code = "<?php\n// Backlink sites list for the backlink pinging tool\n\n\$backlink_sites = [\n";
    
    foreach ($sites as $name => $url) {
        $code .= "    '" . addslashes($name) . "' => '" . addslashes($url) . "',\n";
    }
    
    $code .= "];";
    
    
    $result = file_put_contents($pingSitesFile, $code);
    
    if ($result === false) {
        echo json_encode(['success' => false, 'error' => 'Failed to write to file']);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'Ping sites saved successfully',
            'count' => count($sites)
        ]);
    }
}

function fetchContent($url, $maxRetries = 3, $retryDelay = 5) {
    $attempts = 0;
    $content = false;
    
    while ($attempts < $maxRetries && $content === false) {
        if ($attempts > 0) {
            sleep($retryDelay);
        }
        
        $attempts++;
        
        $content = @file_get_contents($url);
        if ($content === false) {
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; BacklinkPinger/1.0)');
            $content = curl_exec($ch);
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if (curl_errno($ch) || $httpCode == 503) {
                $content = false;
            }
            
            curl_close($ch);
        }
    }
    
    return $content;
}
?>
