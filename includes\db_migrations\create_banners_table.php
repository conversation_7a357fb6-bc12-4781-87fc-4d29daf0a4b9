<?php

function create_banners_table(PDO $pdo): bool
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='banners'");
        if ($stmt->fetch()) {
            return true; 
        }

        
        $sql = "
            CREATE TABLE banners (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                image_filename TEXT NOT NULL,
                link_type TEXT NOT NULL CHECK(link_type IN ('internal', 'external')),
                link_value TEXT,
                link_target TEXT NOT NULL CHECK(link_target IN ('_blank', '_self')) DEFAULT '_self',
                banner_type TEXT NOT NULL CHECK(banner_type IN ('homepage_wide', 'product_small')),
                is_active INTEGER NOT NULL DEFAULT 1,
                visitor_count INTEGER NOT NULL DEFAULT 0,
                display_order INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            )
        ";
        
        $pdo->exec($sql);
        
        
        $pdo->exec("CREATE INDEX idx_banners_type ON banners (banner_type)");
        $pdo->exec("CREATE INDEX idx_banners_active ON banners (is_active)");
        $pdo->exec("CREATE INDEX idx_banners_order ON banners (display_order)");
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

