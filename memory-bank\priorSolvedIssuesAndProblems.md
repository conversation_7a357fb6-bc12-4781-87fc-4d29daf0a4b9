# Prior Solved Issues and Problems

This document tracks all previously solved issues and problems encountered during development. Each entry includes the problem description, root cause analysis, solution implemented, and key learnings to prevent similar issues in the future.

---

## Search Functionality Issues

### Search Substring Matching Issue - Word Boundary Fix (Latest)
**Problem:** Search functionality was returning unwanted substring matches. For example, searching for "bust" would return results containing "robust" and "robuste" because they contain "bust" as a substring, leading to irrelevant search results.

**Root Cause:** Search functions used simple `%searchterm%` LIKE patterns which match any occurrence of the search term within larger words, not just whole words. The pattern `%bust%` would match any text containing "bust" anywhere, including within other words.

**Solution Implemented:**
- Implemented word boundary matching using multiple LIKE patterns for each search field:
  - `% searchterm %` - matches word surrounded by spaces
  - `searchterm %` - matches word at beginning of text
  - `% searchterm` - matches word at end of text
  - `searchterm` - matches exact single word
- Updated three core search functions:
  - `search_site_content()` in `includes/functions.php`
  - `search_site_content_paginated()` in `includes/functions.php`
  - `get_blog_posts()` in `includes/blog_functions.php`
- Each search field (name, description, SKU, keywords, title, content) now uses 4 LIKE conditions with proper parameter binding
- Maintained comprehensive search coverage while eliminating false positive substring matches

**Key Learnings:**
- Always consider whether substring matching is appropriate for search functionality
- Word boundary matching provides more relevant search results for user queries
- Multiple LIKE patterns can effectively simulate word boundary behavior in SQLite
- Proper parameter binding is essential when using multiple search conditions

**Prevention:** When implementing search functionality, evaluate whether users expect exact word matches or substring matches, and implement accordingly.

---

## Admin Panel Routing Issues

### Maintenance Section Page Refresh Fix - Missing Switch Case Handler (Latest)
**Problem:** The maintenance section in the admin panel had a page refresh issue where performing actions like database migrations would redirect users to the dashboard instead of keeping them in the maintenance section. Users would click maintenance actions (like "Migrar Tabelas de Tipos de Arquivo") and after the form submission and page refresh, they would be unexpectedly redirected to the dashboard, losing their current context.

**Root Cause:** The main non-AJAX switch statement in `admin.php` (around line 6400+) was missing a `case 'maintenance':` handler. While AJAX requests were properly handled through a separate switch statement earlier in the file, non-AJAX requests like form submissions that trigger page refreshes were falling through to the default case, which redirects to the dashboard. This created an inconsistent user experience where AJAX navigation worked correctly but form submissions broke the workflow.

**Solution Implemented:**
- Added the missing `case 'maintenance':` to the main switch statement in `admin.php` at line 6409
- Included proper template inclusion: `include_template('backend/maintenance_simple.php', $admin_view_data);`
- Set appropriate page title: `$admin_view_data['page_title'] = "Manutenção - " . $admin_page_title;`
- Maintained consistency with existing section patterns in the codebase
- Ensured both AJAX and non-AJAX workflows work seamlessly

**Technical Implementation:**
```php
case 'maintenance':
    $admin_view_data['page_title'] = "Manutenção - " . $admin_page_title;
    include_template('backend/maintenance_simple.php', $admin_view_data);
    break;
```

**Files Modified:**
- `admin.php` - Added missing maintenance case to main switch statement
- `templates/backend/maintenance_simple.php` - Removed unnecessary "Migrações de Base de Dados" section

**Testing Results:**
- Maintenance section actions now properly refresh within the same section
- Form submissions maintain user context instead of redirecting to dashboard
- Both AJAX navigation and direct URL access work correctly
- Database migration and other maintenance actions function as expected
- User workflow is no longer interrupted by unexpected redirects

**Key Learnings:**
- This is the **third occurrence** of the same pattern: missing cases in the main non-AJAX switch statement (previously fixed for `digital_files` and `digital_products` sections)
- AJAX and non-AJAX request handlers must be kept in sync when adding new admin sections
- The main switch statement in `admin.php` is critical for handling form submissions and page refreshes
- Pattern recognition: when form submissions redirect to dashboard unexpectedly, check for missing switch cases
- This recurring issue indicates a systematic problem in the development workflow

**Prevention Strategy:**
- **Mandatory Checklist**: For every new admin section, verify both AJAX handler AND main switch statement include the new case
- **Code Review Protocol**: Specifically check for consistency between AJAX and non-AJAX handlers in all admin section additions
- **Testing Protocol**: Always test both AJAX navigation AND form submissions/page refreshes for new sections
- **Documentation**: Update this pattern in development guidelines to prevent future occurrences
- **Systematic Approach**: Consider creating a helper function or template for adding new admin sections to ensure consistency

**Quick Identification for Future:**
- **Symptom**: Form submissions or page refreshes redirect to dashboard instead of staying in current section
- **First Check**: Look for missing cases in main non-AJAX switch statement (around line 6400+ in `admin.php`)
- **Verification**: Compare AJAX handler cases with main switch statement cases for completeness
- **Pattern**: If AJAX navigation works but form submissions don't, it's definitely a missing main switch case
- **Root Cause**: Always the same - missing `case 'section_name':` in the main switch statement

**Critical Note for Future Development:**
This is now the **third time** this exact issue has occurred with different admin sections. This indicates a systematic gap in the development process. Any new admin section MUST include both:
1. AJAX handler case (for navigation)
2. Main switch statement case (for form submissions and refreshes)

Failure to include both will result in broken user workflows and unexpected redirects to the dashboard.

---

## Dashboard & Chart Rendering Issues

### Dashboard Chart Rendering Fix - Chart.js Loading Conflicts During AJAX Navigation (Latest)
**Problem:** Dashboard charts (sales chart, order status chart, product category chart) were not rendering properly when navigating to the dashboard via AJAX. Charts would display correctly on direct page loads but fail to appear when using the admin sidebar navigation. The dashboard would load but remain empty where charts should be displayed.

**Root Cause:** Timing conflicts and duplicate script loading between the footer Chart.js initialization and the AJAX navigation system. The issue occurred because:
1. **Race Conditions**: Chart.js library loading competed between footer scripts and AJAX navigation scripts
2. **Duplicate Script Tags**: Multiple attempts to load Chart.js and dashboard scripts created conflicts
3. **Insufficient Retry Logic**: Limited retry attempts and delays weren't adequate for slower connections
4. **Chart Instance Conflicts**: Existing chart instances weren't properly destroyed before re-initialization
5. **State Management**: Dashboard initialization didn't reset properly between AJAX navigations

**Solution Implemented:**

**1. Enhanced AJAX Navigation Chart Loading (`admin-navigation.js`):**
- Increased retry attempts from 3 to 5 with longer delays (500ms, 1000ms, 2000ms, 3000ms, 4000ms)
- Added duplicate script detection to prevent multiple Chart.js and dashboard script tags
- Implemented comprehensive error handling and logging for debugging
- Added state reset functionality to ensure clean initialization
- Improved timing with longer delays between retry attempts

**2. Enhanced Footer Chart Loading (`footer.php`):**
- Added checks to prevent duplicate script loading when Chart.js already exists
- Implemented retry logic with increased delays (1000ms, 2000ms, 3000ms)
- Enhanced logging for better debugging visibility
- Added fallback handling for script loading failures

**3. Improved Dashboard Initialization (`admin-dashboard.js`):**
- Modified `initDashboard()` to always destroy existing chart instances before re-initialization
- Ensured proper cleanup of `salesChart`, `orderStatusChart`, and `productCategoryChart` global variables
- Simplified initialization flow to prevent conflicts
- Added consistent state management across multiple dashboard loads

**Technical Implementation:**
```javascript
// Enhanced retry logic in admin-navigation.js
function initDashboardWithRetry(retryCount = 0) {
    const maxRetries = 5;
    const delays = [500, 1000, 2000, 3000, 4000];
    
    if (typeof Chart !== 'undefined') {
        loadDashboardScript();
        return;
    }
    
    if (retryCount >= maxRetries) {
        console.error('Chart.js failed to load after maximum retries');
        return;
    }
    
    // Check for existing Chart.js script to prevent duplicates
    if (!document.querySelector('script[src*="chart.js"]')) {
        // Load Chart.js dynamically...
    }
    
    setTimeout(() => initDashboardWithRetry(retryCount + 1), delays[retryCount] || 4000);
}

// Always destroy existing charts in admin-dashboard.js
function initDashboard() {
    // Destroy existing chart instances
    if (window.salesChart && typeof window.salesChart.destroy === 'function') {
        window.salesChart.destroy();
        window.salesChart = null;
    }
    if (window.orderStatusChart && typeof window.orderStatusChart.destroy === 'function') {
        window.orderStatusChart.destroy();
        window.orderStatusChart = null;
    }
    if (window.productCategoryChart && typeof window.productCategoryChart.destroy === 'function') {
        window.productCategoryChart.destroy();
        window.productCategoryChart = null;
    }
    
    // Initialize dashboard core and buttons
    initDashboardCore();
    initDashboardButtons();
}
```

**Files Modified:**
- `public/assets/js/admin-navigation.js` - Enhanced Chart.js loading with improved retry logic and duplicate prevention
- `templates/backend/partials/footer.php` - Added duplicate script detection and enhanced error handling
- `public/assets/js/admin-dashboard.js` - Implemented proper chart cleanup and state management

**Testing Results:**
- Dashboard charts now render consistently via both AJAX navigation and direct page loads
- No more empty dashboard sections where charts should appear
- Proper chart destruction prevents memory leaks and conflicts
- Enhanced logging provides better debugging capabilities for future issues
- Retry logic handles slower connections and loading delays effectively

**Key Learnings:**
- Chart.js requires careful timing coordination in AJAX-heavy applications
- Duplicate script loading can cause unpredictable behavior with chart libraries
- Chart instances must be explicitly destroyed before re-initialization to prevent conflicts
- Retry logic should account for varying network conditions with progressive delays
- State management is crucial for dashboard components that reload frequently

**Prevention Strategy:**
- Always implement duplicate script detection when dynamically loading libraries
- Use progressive retry delays rather than fixed intervals for better reliability
- Implement proper cleanup patterns for chart libraries and similar stateful components
- Add comprehensive logging for debugging complex timing issues
- Test both AJAX navigation and direct page loads when implementing dashboard features

**Quick Identification for Future:**
- **Symptom**: Charts not rendering in dashboard after AJAX navigation but working on direct page loads
- **First Check**: Browser console for Chart.js loading errors or "Chart is not defined" messages
- **Debugging**: Look for duplicate script tags in DOM and check timing of Chart.js availability
- **Pattern**: If direct loads work but AJAX doesn't, it's likely a timing/loading conflict issue
- **Tools**: Use browser developer tools to monitor script loading order and timing

**Future Reference:** This pattern of timing conflicts between AJAX navigation and external library loading may occur with other chart libraries (D3.js, Plotly, etc.) or heavy JavaScript components. The solution approach of duplicate detection, progressive retry logic, and proper cleanup can be applied to similar issues.

---

## Performance & Optimization Issues

### Digital Products Section Routing Fix (Latest)
**Problem:** The "Produtos" (Products) section in the admin panel had a broken "Adicionar Detalhes do Produto Digital" button. When users clicked this button after selecting "Produto Digital", they were incorrectly redirected to the dashboard instead of being taken to the digital product details form.

**Root Cause:** The main non-AJAX switch statement in `admin.php` was missing a `case 'digital_products':` handler. While AJAX requests were properly handled through a separate switch statement (around line 659), non-AJAX requests like the "Adicionar Detalhes do Produto Digital" button were falling through to the default case, which redirects to the dashboard. This is identical to the previously fixed digital files issue but affecting a different section.

**Solution Implemented:**
- Added a complete `digital_products` case to the main switch statement in `admin.php` at line 6172
- Included proper action handling for different digital product operations:
  - `edit`: Edit existing digital product details with validation and file type associations
  - `new`: Create new digital product details for a product
  - Default/list: Digital products listing using `digital_products_list.php` template
- Required necessary function files: `digital_product_functions.php` and `digital_files_functions.php`
- Set appropriate page title and extracted `item_id` from GET parameters
- Added proper error handling for missing products with flash messages and redirects
- Maintained consistency with existing section patterns in the codebase

**Technical Implementation:**
```php
case 'digital_products':
    require_once __DIR__ . '/includes/digital_product_functions.php';
    require_once __DIR__ . '/includes/digital_files_functions.php';
    
    $admin_view_data['page_title'] = "Produtos Digitais - " . $admin_page_title;
    
    if ($action === 'edit' && $item_id) {
        $admin_view_data['product_data'] = get_product_by_id($item_id);
        if (!$admin_view_data['product_data']) {
            add_flash_message("Produto não encontrado.", 'danger');
            $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
            return;
        }
        
        $admin_view_data['digital_product'] = get_digital_product_by_product_id($item_id);
        $admin_view_data['all_file_types'] = get_all_file_types();
        
        if ($admin_view_data['digital_product']) {
            $admin_view_data['selected_file_types'] = get_digital_product_file_type_ids($admin_view_data['digital_product']['id']);
        }
        
        include_template('backend/digital_product_form.php', $admin_view_data);
    } elseif ($action === 'new' && $item_id) {
        $admin_view_data['product_data'] = get_product_by_id($item_id);
        if (!$admin_view_data['product_data']) {
            add_flash_message("Produto não encontrado.", 'danger');
            $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
            return;
        }
        
        $admin_view_data['digital_product'] = null;
        $admin_view_data['all_file_types'] = get_all_file_types();
        
        include_template('backend/digital_product_form.php', $admin_view_data);
    } else {
        include_template('backend/digital_products_list.php', $admin_view_data);
    }
    break;
```

**Database Verification:**
- Confirmed `digital_products` table exists with proper schema (id, product_id, digital_file_id, expiry_days, download_limit, created_at, updated_at)
- Verified foreign key relationships between `digital_products`, `products`, and `digital_files` tables are intact
- No database structure changes were required

**Testing Results:**
- "Adicionar Detalhes do Produto Digital" button now correctly navigates to the digital product form
- Both new and edit actions work properly for digital product details
- Product validation prevents access to non-existent products with proper error messages
- AJAX functionality remains unaffected
- All digital product actions (list, new, edit) work as expected

**Key Learnings:**
- This is the second occurrence of the same pattern: missing cases in the main non-AJAX switch statement
- AJAX and non-AJAX request handlers must be kept in sync when adding new admin sections
- The main switch statement in `admin.php` is critical for handling direct URL access and form submissions
- Pattern recognition: when buttons redirect to dashboard unexpectedly, check for missing switch cases

**Prevention Strategy:**
- Implement a systematic check: for every new `case` added to the AJAX handler, verify it exists in the main switch statement
- Create a testing protocol that verifies both AJAX navigation and direct URL access for new sections
- Consider creating a checklist for new admin sections that includes both switch statement handlers
- Code reviews should specifically check for consistency between AJAX and non-AJAX handlers

**Quick Identification for Future:**
- **Symptom**: Admin buttons redirect to dashboard instead of intended page
- **First Check**: Look for missing cases in main non-AJAX switch statement (around line 5800+ in `admin.php`)
- **Verification**: Compare AJAX handler cases (around line 600+) with main switch statement cases
- **Pattern**: If AJAX navigation works but direct links/buttons don't, it's likely a missing main switch case

**Future Reference:** This is now the second time this exact issue occurred (first with `digital_files`, now with `digital_products`). When adding new admin sections, always ensure both the AJAX handler and main switch statement include the new section case to prevent routing issues.

---

### Digital Files Section Pagination Fix
**Problem:** The "Arquivos Digitais" (Digital Files) section in the admin panel had broken pagination functionality. When users tried to navigate between pages or refresh the page, they were redirected to the dashboard instead of staying in the digital files section.

**Root Cause:** The main non-AJAX switch statement in `admin.php` (around line 5817) was missing a `case 'digital_files':` handler. While AJAX requests were properly handled through a separate switch statement, non-AJAX requests like pagination links, page refreshes, and direct URL access were falling through to the default case, which redirects to the dashboard.

**Solution Implemented:**
- Added a complete `digital_files` case to the main switch statement in `admin.php` at line 6172
- Included proper action handling for different digital files operations:
  - `change_file`: Associates digital files with products using `change_digital_file.php` template
  - `manage`: File upload and management using `digital_files_management.php` template  
  - Default/list: File listing with pagination using `digital_files_list.php` template
- Required necessary function files: `digital_files_functions.php` and `digital_product_functions.php`
- Set appropriate page title and extracted `digital_product_id` from GET parameters
- Maintained consistency with existing section patterns in the codebase

**Technical Implementation:**
```php
case 'digital_files':
    require_once 'includes/digital_files_functions.php';
    require_once 'includes/digital_product_functions.php';
    $page_title = 'Arquivos Digitais';
    $digital_product_id = isset($_GET['digital_product_id']) ? (int)$_GET['digital_product_id'] : null;
    
    switch ($action) {
        case 'change_file':
            include 'templates/backend/change_digital_file.php';
            break;
        case 'manage':
            include 'templates/backend/digital_files_management.php';
            break;
        case 'list':
        default:
            include 'templates/backend/digital_files_list.php';
            break;
    }
    break;
```

**Database Verification:**
- Confirmed `digital_files` and `digital_products` tables exist with proper schema
- Verified foreign key relationships between tables are intact
- No database structure changes were required

**Testing Results:**
- Pagination now works correctly in the digital files section
- Page refreshes maintain the current section instead of redirecting to dashboard
- Direct URL access to digital files pages functions properly
- AJAX functionality remains unaffected
- All digital files actions (list, manage, change_file) work as expected

**Key Learnings:**
- AJAX and non-AJAX request handlers must be kept in sync when adding new admin sections
- The main switch statement in `admin.php` is critical for handling direct URL access and page refreshes
- Session and security token issues in pagination are often symptoms of missing route handlers
- Always verify both AJAX and non-AJAX workflows when implementing new admin features

**Prevention Strategy:**
- Implement a testing protocol that verifies both AJAX navigation and direct URL access for new sections
- Consider creating a checklist for new admin sections that includes both switch statement handlers
- Code reviews should specifically check for consistency between AJAX and non-AJAX handlers

**Future Reference:** When adding new admin sections, always ensure both the AJAX handler and main switch statement include the new section case to prevent routing issues.

---

### IP-to-Country Lookup Performance Optimization
**Problem:** The `get_country_from_ip()` function was using CSV file parsing (`asn-country-ipv4.csv`) which was inefficient for large datasets and caused performance bottlenecks during IP lookups for session management.

**Root Cause:** Reading and parsing CSV files line-by-line for each IP lookup request was resource-intensive and didn't scale well with the 136,595 IP range records.

**Solution Implemented:**
- Migrated from CSV file parsing to SQLite database queries using `IPs.sqlite`
- Updated `get_country_from_ip()` function in `includes/functions.php` (line 2531+) to use PDO database connections
- Implemented string-based IP range comparison with proper error handling
- Created composite index `idx_ip_range` on `Starting_IP` and `Ending_IP` columns for query optimization
- Maintained backward compatibility and existing function signature

**Testing Results:**
- Verified accurate country code detection for multiple IP addresses (*******→US, **********→PT)
- Confirmed integration with existing session management and admin interface
- Query execution plan shows proper index usage for optimized performance

**Key Learnings:**
- Database queries with proper indexing significantly outperform CSV file parsing for large datasets
- SQLite is excellent for read-heavy operations like IP geolocation lookups
- Always test with real session data to ensure integration compatibility

**Future Reference:** For similar performance issues with large data lookups, consider database migration with proper indexing over file-based approaches.

---

**31. Admin: Sidebar Navigation Highlighting Issue - "Gerir Imagens" Button Not Active After AJAX Navigation**
*   **Issue**: The "Gerir Imagens" (Manage Images) button in the admin sidebar was not staying visually active (highlighted in blue) after navigating to the images management page via AJAX. When accessing the page directly via URL, the highlighting worked correctly, but clicking the sidebar link would load the correct content without maintaining the active visual state.
*   **Root Cause**: The JavaScript `updateActiveMenuItem()` function in `admin-navigation.js` was only using the `section` parameter to identify which sidebar link should be highlighted. For the "Gerir Imagens" button, which has both `data-section="products"` and `data-action="images"` attributes, this was insufficient. The function needed to consider both `section` and `action` parameters to properly identify the specific menu item.
*   **Solution**: 
    *   **Enhanced JavaScript Logic**: Modified the `updateActiveMenuItem()` function to accept both `section` and `action` parameters and implement smart matching logic that first tries to find a link with both attributes, then falls back to section-only matching for backward compatibility.
    *   **Fixed Sidebar HTML**: Added the missing `data-action="images"` attribute to the "Gerir Imagens" link in the sidebar template.
    *   **Updated Function Call**: Modified the function call to pass both `data.section` and `data.action` from the AJAX response.
*   **Technical Implementation**:
    ```javascript
    // Enhanced updateActiveMenuItem function
    function updateActiveMenuItem(section, action) {
        // Remove active class from all links
        document.querySelectorAll('.admin-sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        let activeLink = null;
        
        // First try to find exact match with both section and action
        if (action) {
            activeLink = document.querySelector(`.admin-sidebar .nav-link[data-section="${section}"][data-action="${action}"]`);
        }
        
        // Fallback to section-only matching
        if (!activeLink) {
            activeLink = document.querySelector(`.admin-sidebar .nav-link[data-section="${section}"]`);
        }
        
        // Apply active state and expand parent menu if needed
        if (activeLink) {
            activeLink.classList.add('active');
            // ... parent menu expansion logic
        }
    }
    ```
    ```html
    <!-- Fixed sidebar link with both data attributes -->
    <a href="admin.php?section=products&action=images&<?= get_session_id_param() ?>" 
       class="nav-link <?= ($section === 'products' && $action === 'images') ? 'active' : '' ?>" 
       data-section="products" 
       data-action="images">
        <i class="bi bi-images"></i> Gerir Imagens
    </a>
    ```
*   **Files Modified**:
    *   `public/assets/js/admin-navigation.js` - Enhanced `updateActiveMenuItem()` function and updated function call
    *   `templates/backend/partials/header.php` - Added missing `data-action="images"` attribute to sidebar link
*   **Prevention & Key Learnings**:
    *   AJAX navigation systems require consistent data attribute patterns for proper state management
    *   JavaScript functions handling navigation state should be flexible enough to handle both specific and general matching scenarios
    *   Sidebar links with specific actions need both `data-section` and `data-action` attributes for precise identification
    *   The admin.php already returns both `section` and `action` in AJAX responses, so the data was available but not being used effectively
*   **User Experience Impact**: The "Gerir Imagens" button now maintains its active visual state (blue highlighting) when navigated to via AJAX, providing consistent visual feedback and improving the admin interface's usability.
*   **Status**: Fixed and tested.
---

**30. Admin: Products Images Template Fatal Error - Undefined Function generate_form_token()**
*   **Issue**: The products images management page (`admin.php?section=images`) was experiencing a fatal error preventing the template from loading. Error logs showed "Fatal error: Uncaught Error: Call to undefined function generate_form_token() in products_images.php:61".
*   **Root Cause**: The `products_images.php` template was calling `generate_form_token()` function which does not exist in the codebase. After searching the codebase, the correct function name is `generate_csrf_token()`. The template was using an incorrect function name for CSRF protection in the image conversion forms.
*   **Solution**: Updated the `templates/backend/products_images.php` template to:
    *   Replace `generate_form_token()` with `generate_csrf_token()` in both WebP and JPEG conversion forms
    *   Update the input field name from `form_token` to `csrf_token` for consistency with the codebase's CSRF protection patterns
*   **Technical Implementation**:
    ```php
    // Before (causing fatal error):
    <input type="hidden" name="form_token" value="<?php echo generate_form_token(); ?>">
    
    // After (fixed):
    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
    ```
*   **Files Modified**:
    *   `templates/backend/products_images.php` - Fixed CSRF token generation in image conversion forms
*   **Template Debugging Enhancement**: During troubleshooting, enhanced the template with debugging code to verify that variables extracted by `include_template()` function were properly available. The `include_template()` function uses `extract($data)` to make array elements available as variables within the included template.
*   **Prevention & Key Learnings**:
    *   Always verify function names exist in the codebase before using them in templates
    *   The codebase uses consistent CSRF protection with `generate_csrf_token()`, `validate_csrf_token()`, and `csrf_input_field()` functions
    *   Template debugging should check for extracted variables (e.g., `$product_images_categorized`) rather than the original data array (e.g., `$admin_view_data['product_images_categorized']`)
    *   Error logs are crucial for identifying fatal errors that prevent template rendering
*   **User Experience Impact**: The products images management page now loads correctly, allowing administrators to view and manage product images by type (JPEG, WebP, PNG, other) and perform image format conversions.
*   **Status**: Fixed and tested.
---

**29. Admin: Session Data Deserialization Fix - Missing Cart Contents on Sessions Page**
*   **Issue**: Cart contents were not displaying on the admin sessions page under "Conteúdo do Carrinho (Sumário)" section. Multiple PHP warnings appeared in the error log related to `unserialize(): Error at offset 0` in `includes/session.php` on line 462.
*   **Root Cause**: The `get_all_sessions_data()` function in `includes/session.php` lacked robust error handling for session data deserialization. While other parts of the codebase had fallback logic for handling both standard serialized and pipe-separated session data formats (`key|value|key|value...`), this function only attempted basic `unserialize()` without error handling, causing failures when session data was corrupted or in unexpected formats.
*   **Solution**: Enhanced the `get_all_sessions_data()` function with comprehensive error handling that:
    *   Uses `@unserialize()` to suppress PHP warnings and prevent error log spam
    *   Implements fallback logic for pipe-separated session data format matching patterns used elsewhere in the codebase
    *   Attempts to unserialize individual values within pipe-separated format
    *   Gracefully handles corrupted, empty, or malformed session data
    *   Maintains backward compatibility with existing session formats
    *   Returns empty cart array when session data cannot be processed
*   **Technical Implementation**:
    ```php
    // Enhanced error handling in get_all_sessions_data()
    $session_data = @unserialize($session['data']);
    if ($session_data === false && !empty($session['data'])) {
        // Handle pipe-separated format: key|value|key|value...
        $parts = explode('|', $session['data']);
        if (count($parts) >= 2 && count($parts) % 2 === 0) {
            $session_data = [];
            for ($i = 0; $i < count($parts); $i += 2) {
                $key = $parts[$i];
                $value = $parts[$i + 1];
                // Try to unserialize individual values
                $unserialized_value = @unserialize($value);
                $session_data[$key] = ($unserialized_value !== false) ? $unserialized_value : $value;
            }
        } else {
            $session_data = [];
        }
    } elseif ($session_data === false) {
        $session_data = [];
    }
    ```
*   **Files Modified**:
    *   `includes/session.php` - Enhanced `get_all_sessions_data()` function with robust error handling
*   **Prevention & Key Learnings**:
    *   Session data deserialization should always include error handling for corrupted or malformed data
    *   Consistent error handling patterns should be applied across all session-related functions
    *   The `@` operator can be useful for suppressing expected warnings when implementing fallback logic
    *   Session data formats can vary, so functions should handle multiple formats gracefully
*   **User Experience Impact**: Admin users can now view cart contents for all active sessions without PHP errors, providing better insight into customer shopping behavior.
*   **Status**: Fixed and tested.
---